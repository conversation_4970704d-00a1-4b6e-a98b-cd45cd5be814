import os
import re
import requests
import sys
import time
import datetime # 用于日志时间戳
import traceback # 用于获取完整的堆栈跟踪
import shutil # 用于更稳健的文件移动
import threading # 用于线程控制和信号量
from concurrent.futures import ThreadPoolExecutor, as_completed # 用于并行处理
from functools import lru_cache
from typing import Optional, Dict, List, Tuple, Any
import json

# Changed from PyQt5 to PyQt6
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QTextEdit, QFileDialog, QMessageBox,
                             QProgressBar, QGroupBox, QLineEdit, QTableWidget,
                             QTableWidgetItem, QHeaderView, QTabWidget, QFormLayout,
                             QComboBox, QPlainTextEdit, QCheckBox, QSplitter,
                             QRadioButton, QProgressDialog)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSettings, QUrl, QSize, QPoint
from PyQt6.QtGui import QDesktopServices, QPalette, QColor # For opening URLs if needed, not directly used for file paths here

# 常量定义
VIDEO_EXTENSIONS = ['.mkv', '.mp4', '.avi', '.mov', '.wmv', '.ts', '.flv', '.webm', '.mpg', '.mpeg']
TMDB_API_BASE_URL = "https://api.themoviedb.org/3"

# 性能配置常量
MAX_CONCURRENT_TRANSFERS = 16
MAX_CONCURRENT_API_CALLS = 8
API_TIMEOUT = 15
CACHE_SIZE = 1000
BATCH_SIZE = 32

# 日志级别
LOG_LEVELS = {
    'DEBUG': 0,
    'INFO': 1,
    'WARNING': 2,
    'ERROR': 3,
    'CRITICAL': 4
}

class PerformanceMonitor:
    """性能监控器"""
    def __init__(self):
        self.start_times = {}
        self.stats = {}

    def start_timer(self, name: str):
        """开始计时"""
        self.start_times[name] = time.time()

    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        if name in self.start_times:
            duration = time.time() - self.start_times[name]
            if name not in self.stats:
                self.stats[name] = []
            self.stats[name].append(duration)
            del self.start_times[name]
            return duration
        return 0.0

    def get_average_time(self, name: str) -> float:
        """获取平均耗时"""
        if name in self.stats and self.stats[name]:
            return sum(self.stats[name]) / len(self.stats[name])
        return 0.0

    def get_stats_summary(self) -> str:
        """获取统计摘要"""
        summary = []
        for name, times in self.stats.items():
            if times:
                avg_time = sum(times) / len(times)
                total_time = sum(times)
                count = len(times)
                summary.append(f"{name}: 平均{avg_time:.2f}s, 总计{total_time:.2f}s, 次数{count}")
        return "\n".join(summary)

DARK_STYLE = """
QWidget {
    background-color: #2b2b2b;
    color: #f0f0f0;
    border: none;
}
QMainWindow, QDialog {
    background-color: #2b2b2b;
}
QGroupBox {
    background-color: #3c3c3c;
    border: 1px solid #555;
    border-radius: 4px;
    margin-top: 1ex;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 3px;
}
QLineEdit, QTextEdit, QPlainTextEdit, QComboBox, QProgressBar {
    background-color: #454545;
    color: #f0f0f0;
    border: 1px solid #666;
    border-radius: 4px;
    padding: 3px;
}
QPushButton {
    background-color: #5a5a5a;
    color: #f0f0f0;
    border: 1px solid #666;
    border-radius: 4px;
    padding: 5px;
}
QPushButton:hover {
    background-color: #6a6a6a;
}
QPushButton:pressed {
    background-color: #7a7a7a;
}
QPushButton:disabled {
    background-color: #444;
    color: #888;
}
QCheckBox, QRadioButton {
    color: #f0f0f0;
}
QTabWidget::pane {
    border-top: 2px solid #3c3c3c;
}
QTabBar::tab {
    background: #3c3c3c;
    color: #f0f0f0;
    padding: 8px;
    border: 1px solid #555;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
QTabBar::tab:selected, QTabBar::tab:hover {
    background: #4a4a4a;
}
QTableWidget {
    background-color: #454545;
    gridline-color: #555;
    border: 1px solid #555;
}
QHeaderView::section {
    background-color: #3c3c3c;
    color: #f0f0f0;
    padding: 4px;
    border: 1px solid #555;
}
QSplitter::handle {
    background: #3c3c3c;
}
QSplitter::handle:horizontal {
    width: 2px;
}
QSplitter::handle:vertical {
    height: 2px;
}
QProgressBar::chunk {
    background-color: #05B8CC;
}
QMessageBox {
    background-color: #2b2b2b;
}
"""

class CacheManager:
    """缓存管理器，统一管理各种缓存"""
    def __init__(self, max_size: int = CACHE_SIZE):
        self.max_size = max_size
        self.api_cache: Dict[str, Any] = {}
        self.path_cache: Dict[str, Optional[str]] = {}
        self.regex_cache: Dict[str, str] = {}
        self.dir_structure_cache: Dict[str, Dict] = {}

    def clear_all(self):
        """清空所有缓存"""
        self.api_cache.clear()
        self.path_cache.clear()
        self.regex_cache.clear()
        self.dir_structure_cache.clear()

    def _trim_cache(self, cache_dict: Dict, max_size: int = None):
        """修剪缓存大小"""
        if max_size is None:
            max_size = self.max_size
        if len(cache_dict) > max_size:
            # 删除最旧的一半条目
            items = list(cache_dict.items())
            for key, _ in items[:len(items)//2]:
                del cache_dict[key]

class RenameWorker(QThread):
    progress_signal = pyqtSignal(int)
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(object) # FIX: Was pyqtSignal(list), caused crash
    preview_signal = pyqtSignal(str, str, str, str)

    def __init__(self, file_paths, moviepilot_api_token, moviepilot_server_url, rename_format,
                 folder_format_template, season_format_template,
                 regex_rules, preview_only=False,
                 custom_season_enabled=False, custom_season_value="",
                 custom_tmdbid_enabled=False, custom_tmdbid_value="",
                 custom_tmdb_media_type="tv",
                 custom_episode_offset_enabled=False, custom_episode_offset_value="",
                 tmdb_api_key="",
                 folder_rename_only_enabled=False, folder_rename_format="",
                 dest_mode="current", custom_dest_path="",
                 use_category_folders=True, use_year_folders=True):
        super().__init__()
        self.file_paths = file_paths
        self.moviepilot_api_token = moviepilot_api_token
        self.moviepilot_server_url = moviepilot_server_url
        self.rename_format = rename_format
        self.folder_format_template = folder_format_template
        self.season_format_template = season_format_template
        self.regex_rules = regex_rules
        self.preview_only = preview_only

        self.custom_season_enabled = custom_season_enabled
        self.custom_season_value = custom_season_value
        self.custom_tmdbid_enabled = custom_tmdbid_enabled
        self.custom_tmdbid_value = custom_tmdbid_value
        self.custom_tmdb_media_type = custom_tmdb_media_type
        self.custom_episode_offset_enabled = custom_episode_offset_enabled
        self.custom_episode_offset_value = custom_episode_offset_value
        self.tmdb_api_key = tmdb_api_key

        # New folder mode attributes
        self.folder_rename_only_enabled = folder_rename_only_enabled
        self.folder_rename_format = folder_rename_format
        self.dest_mode = dest_mode
        self.custom_dest_path = custom_dest_path

        # Organization toggles
        self.use_category_folders = use_category_folders
        self.use_year_folders = use_year_folders

        # 性能优化 - 使用统一的缓存管理器
        self.cache_manager = CacheManager()
        self.transfer_semaphore = threading.Semaphore(MAX_CONCURRENT_TRANSFERS)
        self.api_semaphore = threading.Semaphore(MAX_CONCURRENT_API_CALLS)
        self.performance_monitor = PerformanceMonitor()

        # 请求会话复用 - 优化连接池配置
        self.http_session = self._create_http_session()

        self.results = []
        self._is_interruption_requested = False

    def _create_http_session(self) -> requests.Session:
        """创建优化的HTTP会话"""
        session = requests.Session()
        session.headers.update({
            'accept': 'application/json',
            'User-Agent': 'VideoRenamer/2.0'
        })

        # 优化适配器配置
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=20,
            pool_maxsize=20,
            max_retries=requests.adapters.Retry(
                total=3,
                backoff_factor=0.3,
                status_forcelist=[500, 502, 503, 504]
            ),
            pool_block=False
        )

        session.mount('http://', adapter)
        session.mount('https://', adapter)
        return session
    
    def _find_series_root_folder(self, path):
        """
        从一个文件路径向上查找，直到找到看起来像剧集根目录的文件夹。
        判断依据：文件夹名不叫 "Season X" 或 "Specials" 等常见季文件夹名。
        """
        current_path = os.path.dirname(path)
        while current_path and os.path.basename(current_path):
            folder_name = os.path.basename(current_path)
            # 如果文件夹名符合典型的季/特别篇格式，就继续向上找
            if re.match(r'^(Season|S(eason)?\s*\d{1,2}|Specials|Extras)$', folder_name, re.IGNORECASE):
                parent = os.path.dirname(current_path)
                # 如果没有更上层的目录了，就返回当前目录作为根目录
                if parent == current_path:
                    return current_path
                current_path = parent
            else:
                # 找到了一个看起来不是季文件夹的目录，就把它当作剧集根目录
                return current_path
        return os.path.dirname(path) # Fallback to direct parent

    def requestInterruption(self):
        self._is_interruption_requested = True
        self.log_signal.emit("[DEBUG] 中断请求已接收，将在当前文件处理完毕后停止。")

    def run(self):
        """主运行方法，支持文件夹整理和文件重命名两种模式"""
        self.log_signal.emit(f"[INFO] RenameWorker.run: 开始处理 {len(self.file_paths)} 个文件。")

        try:
            results_summary = {"moved_files": 0, "moved_folders": 0, "total_processed": 0}

            if self.folder_rename_only_enabled:
                results_summary = self._run_folder_mode()
            else:
                results_summary = self._run_file_mode()

            self.finished_signal.emit(results_summary)

        except Exception as e:
            error_details = traceback.format_exc()
            self.log_signal.emit(f"[CRITICAL] 处理过程中发生严重错误: {str(e)}\n{error_details}")
            self.finished_signal.emit({})
        finally:
            # 清理资源
            self._cleanup_resources()
            self.log_signal.emit(f"[INFO] RenameWorker.run: 结束。")

    def _run_folder_mode(self) -> Dict[str, int]:
        """运行文件夹整理模式"""
        self.log_signal.emit("[INFO] 以文件夹整理模式运行。")
        results_summary = {"moved_files": 0, "moved_folders": 0, "total_processed": 0}

        # Step 1: 并行清理路径
        clean_paths = self._clean_paths_parallel()
        if self._is_interruption_requested:
            return results_summary

        # Step 2: 按文件夹分组
        folders_to_process = self._group_files_by_folder(clean_paths)
        self.log_signal.emit(f"[INFO] 发现 {len(folders_to_process)} 个独立的剧集/电影文件夹需要处理。")

        # Step 3: 处理每个文件夹
        return self._process_folders_sequentially(folders_to_process)

    def _run_file_mode(self) -> Dict[str, int]:
        """运行文件重命名模式"""
        self.log_signal.emit("[INFO] 以文件重命名模式运行。")
        results_summary = {"moved_files": 0, "moved_folders": 0, "total_processed": 0}

        total_items = len(self.file_paths)
        max_workers = min(BATCH_SIZE, os.cpu_count() or 4)

        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix='FileProcessor') as executor:
            future_to_path = {executor.submit(self._process_single_file, path): path
                            for path in self.file_paths}
            processed_count = 0

            for future in as_completed(future_to_path):
                if self._is_interruption_requested:
                    break

                try:
                    result = future.result()
                    if result:
                        results_summary["moved_files"] += 1
                except Exception as e:
                    original_path = future_to_path[future]
                    self.log_signal.emit(f"[ERROR] 处理文件 '{original_path}' 时发生意外错误: {traceback.format_exc()}")

                processed_count += 1
                results_summary["total_processed"] = processed_count
                progress = int(processed_count / total_items * 100) if total_items > 0 else 0
                self.progress_signal.emit(progress)

        return results_summary

    def _clean_paths_parallel(self) -> List[str]:
        """并行清理文件路径"""
        clean_paths = []
        max_workers = min(BATCH_SIZE, os.cpu_count() or 8)

        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix='PathCleaner') as executor:
            future_to_path = {executor.submit(self.clean_file_path, path): path
                            for path in self.file_paths}

            for future in as_completed(future_to_path):
                if self._is_interruption_requested:
                    break
                try:
                    result = future.result()
                    if result:
                        clean_paths.append(result)
                except Exception as e:
                    self.log_signal.emit(f"[ERROR] 清理路径时出错: {e}")

        return clean_paths

    def _group_files_by_folder(self, clean_paths: List[str]) -> Dict[str, Dict[str, Any]]:
        """按文件夹分组文件"""
        folders_to_process = {}
        for path in clean_paths:
            series_root = self._find_series_root_folder(path)
            if series_root not in folders_to_process:
                folders_to_process[series_root] = {'rep_file': path, 'all_files': [path]}
            else:
                folders_to_process[series_root]['all_files'].append(path)
        return folders_to_process

    def _process_folders_sequentially(self, folders_to_process: Dict[str, Dict[str, Any]]) -> Dict[str, int]:
        """顺序处理文件夹"""
        results_summary = {"moved_files": 0, "moved_folders": 0, "total_processed": 0}
        total_items = len(folders_to_process)
        processed_count = 0

        for folder_path, data in folders_to_process.items():
            if self._is_interruption_requested:
                break

            folder_start_time = time.time()
            timestamp = datetime.datetime.now().strftime('%H:%M:%S')
            folder_name = os.path.basename(folder_path)
            self.log_signal.emit(f"--- [{timestamp}] 开始处理文件夹: {folder_name} ---")

            try:
                result = self.rename_containing_folder(folder_path, data['rep_file'], len(data['all_files']))
                if result:
                    results_summary["moved_files"] += result.get("moved_files", 0)
                    results_summary["moved_folders"] += result.get("moved_folders", 0)

                duration = time.time() - folder_start_time
                timestamp = datetime.datetime.now().strftime('%H:%M:%S')
                self.log_signal.emit(f"--- [{timestamp}] 文件夹处理完成: {folder_name} (耗时: {duration:.2f} 秒) ---")

            except Exception as e:
                error_details = traceback.format_exc()
                self.log_signal.emit(f"[ERROR] 处理文件夹 '{folder_name}' 时发生意外错误: {str(e)}\n{error_details}")

            processed_count += 1
            results_summary["total_processed"] = processed_count
            progress = int(processed_count / total_items * 100) if total_items > 0 else 0
            self.progress_signal.emit(progress)

        return results_summary

    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 输出性能统计
            if hasattr(self, 'performance_monitor'):
                stats = self.performance_monitor.get_stats_summary()
                if stats:
                    self.log_signal.emit(f"[INFO] 性能统计:\n{stats}")

            # 关闭HTTP会话
            if hasattr(self, 'http_session'):
                self.http_session.close()

            # 清理缓存
            if hasattr(self, 'cache_manager'):
                cache_stats = self._get_cache_stats()
                self.log_signal.emit(f"[INFO] 缓存统计: {cache_stats}")
                self.cache_manager.clear_all()

        except Exception as e:
            self.log_signal.emit(f"[WARNING] 清理资源时出错: {e}")

    def _get_cache_stats(self) -> str:
        """获取缓存统计信息"""
        try:
            api_count = len(self.cache_manager.api_cache)
            path_count = len(self.cache_manager.path_cache)
            regex_count = len(self.cache_manager.regex_cache)
            dir_count = len(self.cache_manager.dir_structure_cache)

            return f"API缓存: {api_count}, 路径缓存: {path_count}, 正则缓存: {regex_count}, 目录缓存: {dir_count}"
        except Exception:
            return "缓存统计获取失败"

    def _process_single_file(self, video_path_str_from_gui):
        """处理单个文件，用于并行处理"""
        try:
            clean_path = self.clean_file_path(video_path_str_from_gui)
            if not clean_path:
                self.log_signal.emit(f"[WARNING] _process_single_file: 跳过无效或不存在的文件路径: {video_path_str_from_gui}")
                return None

            self.log_signal.emit(f"[DEBUG] _process_single_file: 清理后路径: {clean_path}")
            
            result = self.rename_video_file(clean_path)
            return result
        except Exception as e:
            self.log_signal.emit(f"[ERROR] _process_single_file: 处理文件 {os.path.basename(video_path_str_from_gui)} 时出错: {str(e)}")
            raise

    def clean_file_path(self, path_input_str: str) -> Optional[str]:
        """清理和验证文件路径，使用缓存提高性能"""
        # 使用路径缓存以提高性能
        if path_input_str in self.cache_manager.path_cache:
            return self.cache_manager.path_cache[path_input_str]

        self.log_signal.emit(f"[DEBUG] clean_file_path: 输入路径 '{path_input_str}'")
        path = path_input_str.strip()
        if not path:
            self.log_signal.emit("[DEBUG] clean_file_path: 输入路径为空。")
            return None

        try:
            normalized_path = self._normalize_file_path(path)
            if not normalized_path:
                return None

            # 验证路径存在性
            result = self._validate_file_path(normalized_path, path_input_str)

            # 缓存结果
            self.cache_manager.path_cache[path_input_str] = result
            self.cache_manager._trim_cache(self.cache_manager.path_cache)

            return result

        except Exception as e:
            self.log_signal.emit(f"[ERROR] clean_file_path: 处理路径 '{path_input_str}' 时出错: {e}")
            return None

    def _normalize_file_path(self, path: str) -> Optional[str]:
        """规范化文件路径"""
        original_path = path

        # 处理file:// URL
        if path.startswith('file:'):
            path = self._handle_file_url(path)

        # URL解码
        path = path.replace('%20', ' ')

        # 路径规范化
        try:
            if os.name == 'nt' and path.startswith('\\\\') and not path.startswith('\\\\\\'):
                normalized_path = path
            else:
                normalized_path = os.path.normpath(path)

            self.log_signal.emit(f"[DEBUG] _normalize_file_path: '{original_path}' -> '{normalized_path}'")
            return normalized_path

        except Exception as e:
            self.log_signal.emit(f"[ERROR] _normalize_file_path: 规范化路径失败: {e}")
            return None

    def _handle_file_url(self, path: str) -> str:
        """处理file:// URL格式的路径"""
        qurl_temp = QUrl(path)
        if qurl_temp.isLocalFile():
            local_path = qurl_temp.toLocalFile()
            if local_path:
                return local_path

        # 手动处理各种file:// 格式
        if path.startswith('file:////'):
            if os.name == 'nt':
                return path[len('file:'):]  # 保留UNC路径的//
            else:
                return path[len('file:///'):]
        elif path.startswith('file:///'):
            path = path[len('file:///'):]
            if os.name != 'nt' and not os.path.isabs(path):
                path = '/' + path
        elif path.startswith('file://'):
            path = path[len('file://'):]
        elif path.startswith('file:/'):
            path = path[len('file:/'):]

        # Windows路径调整
        if (os.name == 'nt' and path.startswith('/') and
            not path.startswith('//') and len(path) > 2 and path[2] == ':'):
            path = path[1:]

        return path

    def _validate_file_path(self, normalized_path: str, original_path: str) -> Optional[str]:
        """验证文件路径是否存在"""
        # CloudDrive路径特殊处理
        if ':\\CloudDrive' in normalized_path or ':/CloudDrive' in normalized_path:
            self.log_signal.emit(f"[DEBUG] _validate_file_path: 检测到CloudDrive路径: '{normalized_path}'")

            # CloudDrive缓存检查
            cache_key = f"exists_{normalized_path}"
            if cache_key in self.cache_manager.path_cache:
                return self.cache_manager.path_cache[cache_key]

            if os.path.exists(normalized_path) and os.path.isfile(normalized_path):
                self.cache_manager.path_cache[cache_key] = normalized_path
                return normalized_path
            else:
                self.log_signal.emit(f"[WARNING] _validate_file_path: CloudDrive路径无效: '{normalized_path}'")
                return None

        # 常规路径验证
        if os.path.exists(normalized_path) and os.path.isfile(normalized_path):
            self.log_signal.emit(f"[DEBUG] _validate_file_path: 路径有效: '{normalized_path}'")
            return normalized_path
        else:
            self.log_signal.emit(f"[WARNING] _validate_file_path: 路径无效: '{normalized_path}' (原始: '{original_path}')")
            return None

    def apply_regex_rules(self, text: str, is_filename: bool = True) -> str:
        """应用正则表达式规则，使用缓存提高性能"""
        if not is_filename or not self.regex_rules:
            return text

        original_text = text

        # 使用缓存避免重复计算
        cache_key = f"{text}_{is_filename}_{hash(str(self.regex_rules))}"
        if cache_key in self.cache_manager.regex_cache:
            return self.cache_manager.regex_cache[cache_key]

        # 应用所有正则规则
        for pattern_str, replacement in self.regex_rules:
            try:
                text = self._apply_single_regex_rule(text, pattern_str, replacement)
            except re.error as e:
                self.log_signal.emit(f"[ERROR] apply_regex_rules: 正则错误: {pattern_str}=>{replacement}: {e}")
                continue

        if original_text != text:
            self.log_signal.emit(f"[DEBUG] apply_regex_rules: 最终结果，从 '{original_text}' 变为 '{text}'")

        # 保存到缓存
        self.cache_manager.regex_cache[cache_key] = text
        self.cache_manager._trim_cache(self.cache_manager.regex_cache)
        return text

    def _apply_single_regex_rule(self, text: str, pattern_str: str, replacement: str) -> str:
        """应用单个正则表达式规则"""
        flags = 0
        temp_pattern_str = pattern_str

        # 处理忽略大小写标志
        if temp_pattern_str.startswith('(?i)') or temp_pattern_str.startswith('(?I)'):
            temp_pattern_str = temp_pattern_str[4:]
            flags = re.IGNORECASE

        text_before_sub = text
        text = re.sub(temp_pattern_str, replacement, text, flags=flags)

        if text != text_before_sub:
            self.log_signal.emit(f"[DEBUG] _apply_single_regex_rule: 应用规则 '{pattern_str}' => '{replacement}'。 从 '{text_before_sub}' 变为 '{text}'")

        return text
        
    def retry_file_operation(self, operation_func, *args, max_retries=3, delay=1):
        """对文件操作进行多次尝试，特别适合网盘操作"""
        last_exception = None
        for attempt in range(max_retries):
            try:
                return operation_func(*args)
            except Exception as e:
                last_exception = e
                if attempt < max_retries - 1:
                    self.log_signal.emit(f"[RETRY] 操作失败，{delay}秒后第{attempt+2}次尝试: {e}")
                    time.sleep(delay)
                    delay *= 2  # 指数退避
                else:
                    self.log_signal.emit(f"[ERROR] 操作在{max_retries}次尝试后失败: {e}")
        raise last_exception  # 如果所有尝试都失败，抛出最后一个异常
    
    def throttled_move(self, source: str, dest: str) -> bool:
        """限制并发的文件移动操作，支持性能监控"""
        self.performance_monitor.start_timer("file_move")

        try:
            with self.transfer_semaphore:
                # 检查源文件是否存在
                if not os.path.exists(source):
                    self.log_signal.emit(f"[ERROR] throttled_move: 源文件不存在: {source}")
                    return False

                # 确保目标目录存在
                dest_dir = os.path.dirname(dest)
                if dest_dir and not os.path.exists(dest_dir):
                    os.makedirs(dest_dir, exist_ok=True)

                # 检查目标文件是否已存在
                if os.path.exists(dest):
                    if os.path.samefile(source, dest):
                        self.log_signal.emit(f"[INFO] throttled_move: 源文件和目标文件相同，跳过: {source}")
                        return True
                    else:
                        self.log_signal.emit(f"[ERROR] throttled_move: 目标文件已存在: {dest}")
                        return False

                # 执行移动操作
                result = self.retry_file_operation(self._safe_move, source, dest)

                duration = self.performance_monitor.end_timer("file_move")
                file_size = os.path.getsize(dest) if os.path.exists(dest) else 0
                speed = file_size / (1024 * 1024 * max(duration, 0.001))  # MB/s

                self.log_signal.emit(f"[DEBUG] throttled_move: 移动完成 {speed:.2f} MB/s")
                return result

        except Exception as e:
            self.performance_monitor.end_timer("file_move")
            self.log_signal.emit(f"[ERROR] throttled_move: 移动文件失败 '{source}' -> '{dest}': {e}")
            return False

    def _safe_move(self, source: str, dest: str):
        """安全的文件移动操作"""
        try:
            # 尝试使用os.rename（同一文件系统内最快）
            os.rename(source, dest)
        except OSError:
            # 跨文件系统移动，使用shutil.move
            shutil.move(source, dest)
            
    def cache_directory_structure(self, folder_path: str) -> Dict[str, Dict[str, List[str]]]:
        """缓存目录结构以减少文件系统查询"""
        if folder_path in self.cache_manager.dir_structure_cache:
            return self.cache_manager.dir_structure_cache[folder_path]

        structure = {}
        try:
            for root, dirs, files in os.walk(folder_path):
                relative_path = os.path.relpath(root, folder_path)
                video_files = [file for file in files
                             if os.path.splitext(file)[1].lower() in VIDEO_EXTENSIONS]
                structure[relative_path] = {"dirs": dirs, "files": video_files}

            self.cache_manager.dir_structure_cache[folder_path] = structure
            self.cache_manager._trim_cache(self.cache_manager.dir_structure_cache)
            return structure
        except Exception as e:
            self.log_signal.emit(f"[ERROR] cache_directory_structure: 缓存目录结构失败 '{folder_path}': {e}")
            return {}

    def fetch_moviepilot_video_info(self, video_filename: str, token: str) -> Dict[str, Any]:
        """获取MoviePilot视频信息，使用缓存和信号量控制并发"""
        # 使用缓存避免重复API调用
        cache_key = f"mp_{video_filename}_{hash(token)}"
        if cache_key in self.cache_manager.api_cache:
            self.log_signal.emit(f"[INFO] fetch_moviepilot_video_info: 使用缓存数据: '{video_filename}'")
            return self.cache_manager.api_cache[cache_key]

        self.log_signal.emit(f"[INFO] fetch_moviepilot_video_info: 正在为 '{video_filename}' 获取MoviePilot信息。")

        # 使用信号量控制API并发数
        with self.api_semaphore:
            try:
                result = self._make_moviepilot_request(video_filename, token)

                # 只缓存成功的响应
                if result.get("success", False):
                    self.cache_manager.api_cache[cache_key] = result
                    self.cache_manager._trim_cache(self.cache_manager.api_cache)

                return result

            except Exception as e:
                err_msg = f"获取MoviePilot信息未知错误: {e}"
                self.log_signal.emit(f"[ERROR] fetch_moviepilot_video_info: {err_msg}\n{traceback.format_exc()}")
                return {"success": False, "error": err_msg, "message": err_msg}

    def _make_moviepilot_request(self, video_filename: str, token: str) -> Dict[str, Any]:
        """执行MoviePilot API请求"""
        try:
            video_fn_enc = requests.utils.quote(video_filename)
            url = f'{self.moviepilot_server_url}/api/v1/media/recognize_file2?path={video_fn_enc}&token={token}'
            log_url = f'{self.moviepilot_server_url}/api/v1/media/recognize_file2?path={video_fn_enc}&token=***'
            self.log_signal.emit(f"[DEBUG] _make_moviepilot_request: 请求URL: {log_url}")

            # 使用预设的会话进行请求
            response = self.http_session.get(url, timeout=API_TIMEOUT)
            self.log_signal.emit(f"[DEBUG] _make_moviepilot_request: 响应状态码: {response.status_code}")

            response.raise_for_status()
            response_json = response.json()

            return self._process_moviepilot_response(response_json, video_filename)

        except requests.exceptions.HTTPError as e:
            return self._handle_http_error(e, video_filename)
        except requests.exceptions.Timeout:
            err_msg = "MoviePilot API请求超时"
            self.log_signal.emit(f"[ERROR] _make_moviepilot_request: {err_msg} ({video_filename})")
            return {"success": False, "error": err_msg, "message": err_msg}
        except requests.exceptions.RequestException as e:
            err_msg = f"MoviePilot API请求错误: {e}"
            self.log_signal.emit(f"[ERROR] _make_moviepilot_request: {err_msg} ({video_filename})")
            return {"success": False, "error": err_msg, "message": err_msg}

    def _process_moviepilot_response(self, response_json: Any, video_filename: str) -> Dict[str, Any]:
        """处理MoviePilot API响应"""
        # 健壮性检查
        if response_json is None:
            self.log_signal.emit(f"[ERROR] _process_moviepilot_response: API返回null ({video_filename})")
            return {"success": False, "error": "API返回null", "message": "Null response"}

        if not response_json:
            self.log_signal.emit(f"[ERROR] _process_moviepilot_response: API返回空对象 ({video_filename})")
            return {"success": False, "error": "API返回空对象", "message": "Empty JSON response"}

        if isinstance(response_json, dict) and ("media_info" in response_json or "meta_info" in response_json):
            self._log_media_info(response_json)
            return {"success": True, "data": response_json}
        else:
            error_detail = str(response_json)[:200]
            self.log_signal.emit(f"[ERROR] _process_moviepilot_response: API返回意外结构 ({video_filename}): {error_detail}")
            return {"success": False, "error": "API返回意外结构", "message": error_detail}

    def _log_media_info(self, response_json: Dict[str, Any]):
        """记录媒体信息到日志"""
        media_info = response_json.get('media_info', {})
        if not media_info:
            return

        info_items = [
            ('genres', '分类信息'),
            ('origin_country', '原产国信息'),
            ('original_language', '原语言'),
            ('category', '二级分类')
        ]

        for key, desc in info_items:
            value = media_info.get(key)
            if value:
                self.log_signal.emit(f"[DEBUG] _log_media_info: 获取到{desc}: {value}")

    def _handle_http_error(self, error: requests.exceptions.HTTPError, video_filename: str) -> Dict[str, Any]:
        """处理HTTP错误"""
        err_msg = f"HTTP错误 {error.response.status_code}"
        try:
            error_content = error.response.json()
            if error_content is None:
                err_detail = "API返回null"
            else:
                err_detail = (error_content.get('detail', error_content.get('error', str(error)))
                            if isinstance(error_content, dict) else str(error_content))
            err_msg += f": {err_detail}"
            self.log_signal.emit(f"[ERROR] _handle_http_error: MoviePilot API失败 ({video_filename}): {err_msg}")
        except (ValueError, requests.exceptions.JSONDecodeError):
            err_msg += f": {error.response.text[:200]}"
            self.log_signal.emit(f"[ERROR] _handle_http_error: MoviePilot API失败 ({video_filename}): {err_msg}")

        return {"success": False, "error": err_msg, "message": err_msg}

    def _fetch_tmdb_details_from_api(self, tmdb_id, api_key, media_type="tv"):
        self.log_signal.emit(f"[INFO] _fetch_tmdb_details_from_api: 正在为ID '{tmdb_id}' (类型: {media_type.upper()}) 获取TMDB信息。")
        if not api_key:
            self.log_signal.emit("[ERROR] _fetch_tmdb_details_from_api: TMDB API Key未提供。")
            return None
        if media_type not in ["tv", "movie"]:
            self.log_signal.emit(f"[WARNING] _fetch_tmdb_details_from_api: 无效的TMDB媒体类型: {media_type}。将默认为 'tv'。")
            media_type = "tv"

        url = f"{TMDB_API_BASE_URL}/{media_type}/{tmdb_id}?api_key={api_key}&language=zh-CN"
        self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 请求URL (api_key部分省略): {TMDB_API_BASE_URL}/{media_type}/{tmdb_id}?api_key=***&language=zh-CN")

        data_to_return = {'id': tmdb_id, 'type': media_type}
        try:
            response = requests.get(url, timeout=10)
            self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 响应状态码: {response.status_code}")
            response.raise_for_status()

            tmdb_data = response.json()
            self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 响应JSON (部分): Keys: {list(tmdb_data.keys())}")

            if media_type == "tv":
                data_to_return['title'] = tmdb_data.get('name', tmdb_data.get('original_name', '未知TMDB标题'))
                air_date = tmdb_data.get('first_air_date', '')
                data_to_return['year'] = air_date.split('-')[0] if air_date else '未知年份'
                
                genres = tmdb_data.get('genres', [])
                if genres:
                    data_to_return['genres'] = [g for g in genres if g.get('name')] # store full genre dicts
                    self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 获取到类型: {[g.get('name') for g in genres]}")
                
                origin_countries = tmdb_data.get('origin_country', [])
                if origin_countries:
                    data_to_return['origin_countries'] = origin_countries
                    self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 获取到原产国: {origin_countries}")
                
            elif media_type == "movie":
                data_to_return['title'] = tmdb_data.get('title', tmdb_data.get('original_title', '未知TMDB标题'))
                release_date = tmdb_data.get('release_date', '')
                data_to_return['year'] = release_date.split('-')[0] if release_date else '未知年份'
                
                genres = tmdb_data.get('genres', [])
                if genres:
                    data_to_return['genres'] = [g for g in genres if g.get('name')]
                    self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 获取到类型: {[g.get('name') for g in genres]}")
                
                production_countries = tmdb_data.get('production_countries', [])
                if production_countries:
                    data_to_return['production_countries'] = [c.get('iso_3166_1') for c in production_countries if c.get('iso_3166_1')]
                    self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 获取到制作国家: {data_to_return['production_countries']}")
                
                original_language = tmdb_data.get('original_language')
                if original_language:
                    data_to_return['original_language'] = original_language
                    self.log_signal.emit(f"[DEBUG] _fetch_tmdb_details_from_api: 获取到原语言: {original_language}")

            self.log_signal.emit(f"[INFO] _fetch_tmdb_details_from_api: TMDB {media_type.upper()} 信息获取成功: ID {tmdb_id} -> {data_to_return['title']} ({data_to_return['year']})")
            return data_to_return

        except requests.exceptions.HTTPError as e:
            self.log_signal.emit(f"[ERROR] _fetch_tmdb_details_from_api: TMDB {media_type.upper()} API HTTP错误 (ID: {tmdb_id}): {e.response.status_code} - 响应 (前100字符): {e.response.text[:100]}")
            return None
        except requests.exceptions.RequestException as e:
            self.log_signal.emit(f"[ERROR] _fetch_tmdb_details_from_api: TMDB {media_type.upper()} API请求错误 (ID: {tmdb_id}): {str(e)}")
            return None
        except Exception as e:
            self.log_signal.emit(f"[ERROR] _fetch_tmdb_details_from_api: 解析TMDB {media_type.upper()} API响应时出错 (ID: {tmdb_id}): {str(e)}\n{traceback.format_exc()}")
            return None

    def rename_video_file(self, video_os_path):
        self.log_signal.emit(f"[INFO] rename_video_file: 开始处理文件: {video_os_path}")
        video_filename = os.path.basename(video_os_path)
        video_filename_no_ext, file_extension = os.path.splitext(video_filename)
        self.log_signal.emit(f"[DEBUG] rename_video_file: 原文件名 (无扩展名): '{video_filename_no_ext}', 扩展名: '{file_extension}'")

        title = "未知标题"
        year = "未知年份"
        tmdbid_for_naming = "N/A"
        season_number_raw = None
        episode_number_raw = None
        current_media_type = self.custom_tmdb_media_type if self.custom_tmdbid_enabled else None

        if self.custom_tmdbid_enabled and self.custom_tmdbid_value and self.tmdb_api_key:
            self.log_signal.emit(f"[INFO] rename_video_file: 使用自定义TMDB ID: {self.custom_tmdbid_value} (类型: {self.custom_tmdb_media_type.upper()})")
            tmdb_info_custom = self._fetch_tmdb_details_from_api(
                self.custom_tmdbid_value,
                self.tmdb_api_key,
                self.custom_tmdb_media_type
            )
            if tmdb_info_custom:
                title = tmdb_info_custom.get('title', title)
                year = tmdb_info_custom.get('year', year)
                tmdbid_for_naming = str(self.custom_tmdbid_value)
                self.log_signal.emit(f"[DEBUG] rename_video_file: 自定义TMDB信息已应用: 标题='{title}', 年份='{year}', TMDBID='{tmdbid_for_naming}', 类型='{current_media_type}'")
            else:
                self.log_signal.emit(f"[WARNING] rename_video_file: 自定义TMDB ID {self.custom_tmdbid_value} ({self.custom_tmdb_media_type.upper()}) 获取信息失败。将尝试MoviePilot API。")

        self.log_signal.emit(f"[INFO] rename_video_file: 尝试从MoviePilot API获取 '{video_filename}' 的信息。")
        moviepilot_response = self.fetch_moviepilot_video_info(video_filename, self.moviepilot_api_token)

        if moviepilot_response and moviepilot_response.get("success", False):
            api_data = moviepilot_response.get('data', {})
            meta_info = api_data.get('meta_info', {})
            media_info = api_data.get('media_info', {})
            self.log_signal.emit(f"[DEBUG] rename_video_file: MoviePilot API 响应成功。")

            if current_media_type is None:
                mp_type_str = meta_info.get('type', media_info.get('type', "未知")).lower()
                if "电视剧" in mp_type_str or "tv" in mp_type_str: current_media_type = "tv"
                elif "电影" in mp_type_str or "movie" in mp_type_str: current_media_type = "movie"
                else: current_media_type = "tv" # Default if unsure
                self.log_signal.emit(f"[DEBUG] rename_video_file: 从MoviePilot推断媒体类型: {current_media_type}")

            if not (self.custom_tmdbid_enabled and self.custom_tmdbid_value and tmdbid_for_naming == self.custom_tmdbid_value):
                title_mp = media_info.get('title') or media_info.get('en_title') or meta_info.get('name')
                if title_mp: title = str(title_mp).strip()
                if media_info.get('year'): year = str(media_info.get('year')).strip()
                if media_info.get('tmdb_id'): tmdbid_for_naming = str(media_info.get('tmdb_id')).strip()
                self.log_signal.emit(f"[DEBUG] rename_video_file: MoviePilot数据应用: 标题='{title}', 年份='{year}', TMDBID='{tmdbid_for_naming}'")

            season_episode_str = meta_info.get('season_episode')
            if season_episode_str:
                match_se = re.match(r'S(\d+)\s*E(\d+)', season_episode_str, re.IGNORECASE)
                if match_se:
                    season_number_raw = match_se.group(1)
                    episode_number_raw = match_se.group(2)
                    self.log_signal.emit(f"[DEBUG] rename_video_file: 从meta_info.season_episode提取: 季='{season_number_raw}', 集='{episode_number_raw}'")

            if episode_number_raw is None and meta_info.get('begin_episode') is not None:
                episode_number_raw = str(meta_info.get('begin_episode'))
                self.log_signal.emit(f"[DEBUG] rename_video_file: 从meta_info.begin_episode提取: 集='{episode_number_raw}'")

            if season_number_raw is None and media_info.get('season_info'):
                first_season_info = media_info['season_info'][0] if isinstance(media_info['season_info'], list) and media_info['season_info'] else None
                if first_season_info and first_season_info.get('season_number') is not None:
                    season_number_raw = str(first_season_info.get('season_number'))
                    self.log_signal.emit(f"[DEBUG] rename_video_file: 从media_info.season_info提取: 季='{season_number_raw}'")
            
            if season_number_raw is None and episode_number_raw is not None and current_media_type == "tv":
                season_number_raw = "1" # Default season to 1 if episode is present for TV
                self.log_signal.emit(f"[DEBUG] rename_video_file: 有集号无季号 (电视剧)，默认季为1。")

            self.log_signal.emit(f"[INFO] rename_video_file: MoviePilot识别结果: 标题='{title}', 年份='{year}', TMDBID='{tmdbid_for_naming}', 原始季='{season_number_raw}', 原始集='{episode_number_raw}', 类型='{current_media_type}'")
        else:
            self.log_signal.emit(f"[WARNING] rename_video_file: MoviePilot API未能获取 '{video_filename}' 的有效信息。")
            if not (self.custom_tmdbid_enabled and self.custom_tmdbid_value and tmdbid_for_naming == self.custom_tmdbid_value):
                 self.log_signal.emit(f"[WARNING] rename_video_file: 无法从任何来源获取 '{video_filename}' 的基础信息。")
            if current_media_type is None:
                current_media_type = "tv" 
                self.log_signal.emit(f"[DEBUG] rename_video_file: MoviePilot API失败且无自定义TMDB类型，默认媒体类型: {current_media_type}")


        if self.custom_season_enabled and self.custom_season_value:
            try:
                season_number_raw = str(int(self.custom_season_value)) 
                self.log_signal.emit(f"[INFO] rename_video_file: 使用自定义季度: {season_number_raw}")
            except ValueError:
                self.log_signal.emit(f"[WARNING] rename_video_file: 自定义季度值 '{self.custom_season_value}' 无效。")

        season_str_padded = ""
        season_str_unpadded = ""
        if season_number_raw is not None and str(season_number_raw).strip():
            try:
                season_int_val = int(season_number_raw)
                season_str_unpadded = str(season_int_val)
                season_str_padded = season_str_unpadded.zfill(2)
            except ValueError:
                self.log_signal.emit(f"[WARNING] rename_video_file: 季信息'{season_number_raw}'无效。")
        
        if not season_str_padded and current_media_type == "tv" and not (self.custom_tmdb_media_type == "movie" and not self.custom_season_enabled):
            season_str_padded = "01"
            season_str_unpadded = "1"
            self.log_signal.emit(f"[DEBUG] rename_video_file: 季信息缺失或无效 (非电影模式)，使用默认季: {season_str_padded}")


        calculated_episode_number_int = None
        if episode_number_raw is not None and str(episode_number_raw).strip():
            try: calculated_episode_number_int = int(episode_number_raw)
            except ValueError: self.log_signal.emit(f"[WARNING] rename_video_file: 原始集信息'{episode_number_raw}'无效。")

        if self.custom_episode_offset_enabled and self.custom_episode_offset_value:
            try:
                offset_val = int(self.custom_episode_offset_value)
                base_episode_for_offset = calculated_episode_number_int if calculated_episode_number_int is not None else 0 
                calculated_episode_number_int = base_episode_for_offset + offset_val
                if calculated_episode_number_int <= 0: calculated_episode_number_int = 1
                self.log_signal.emit(f"[INFO] rename_video_file: 自定义集数偏移后: {calculated_episode_number_int}")
            except ValueError: self.log_signal.emit(f"[ERROR] rename_video_file: 自定义集数偏移值 '{self.custom_episode_offset_value}' 无效。")

        episode_str_padded = ""
        if calculated_episode_number_int is not None:
            episode_str_padded = str(calculated_episode_number_int).zfill(2)
        elif current_media_type == "tv" and not (self.custom_tmdb_media_type == "movie" and not self.custom_season_enabled):
            episode_str_padded = "01"
            self.log_signal.emit(f"[DEBUG] rename_video_file: 集信息缺失或无效 (非电影模式)，使用默认集: {episode_str_padded}")


        title = title if title.strip() and title != "未知标题" else video_filename_no_ext
        year = year if year.strip() and year != "未知年份" else "0000"
        self.log_signal.emit(f"[DEBUG] rename_video_file: 最终元数据: 标题='{title}', 年份='{year}', TMDBID='{tmdbid_for_naming}', 季(补)='{season_str_padded}', 季(无)='{season_str_unpadded}', 集='{episode_str_padded}', 类型='{current_media_type}'")

        format_data = {'title': title, 'year': year, 'tmdbid': tmdbid_for_naming,
                       'season': season_str_padded, 'season_int': season_str_unpadded,
                       'episode': episode_str_padded, 'filename': video_filename_no_ext}
        self.log_signal.emit(f"[DEBUG] rename_video_file: 用于格式化的数据: {format_data}")

        new_filename_final = self.rename_format
        if current_media_type == "movie" and not self.custom_season_enabled:
            temp_fn = self.rename_format
            for k, v in format_data.items(): temp_fn = temp_fn.replace(f'{{{k}}}', str(v))
            
            if not format_data['season'] and not format_data['episode']:
                temp_fn = re.sub(r'S\w*[\s\._-]*E\w*', '', temp_fn, flags=re.IGNORECASE).strip()
                temp_fn = re.sub(r'Season\s*\w*[\s\._-]*Episode\s*\w*', '', temp_fn, flags=re.IGNORECASE).strip()
            elif format_data['season'] and not format_data['episode']:
                temp_fn = re.sub(r'(S\w*[\s\._-]*)E(?!\w)', r'\1', temp_fn, flags=re.IGNORECASE).strip()
                temp_fn = re.sub(r'(Season\s*\w*[\s\._-]*)Episode(?!\w)', r'\1', temp_fn, flags=re.IGNORECASE).strip()
            elif not format_data['season'] and format_data['episode']:
                 temp_fn = re.sub(r'S[\s\._-]*(E\w*)', r'\1', temp_fn, flags=re.IGNORECASE).strip()
                 temp_fn = re.sub(r'Season[\s\._-]*(Episode\s*\w*)', r'\1', temp_fn, flags=re.IGNORECASE).strip()

            temp_fn = re.sub(r'[\s\._-]+$', '', temp_fn)
            temp_fn = re.sub(r'^[\s\._-]+', '', temp_fn)
            temp_fn = re.sub(r'([\s\._-])[\s\._]+', r'\1', temp_fn)
            temp_fn = temp_fn.replace('()', '').replace('[]', '').strip(' ._-')
            new_filename_final = temp_fn
            self.log_signal.emit(f"[DEBUG] rename_video_file: 为电影调整S/E部分后文件名: '{new_filename_final}'")
        else:
            for k, v in format_data.items(): new_filename_final = new_filename_final.replace(f'{{{k}}}', str(v))
        
        self.log_signal.emit(f"[DEBUG] rename_video_file: 占位符替换后文件名: '{new_filename_final}'")
        new_filename_final = self.apply_regex_rules(new_filename_final)
        new_filename_final = re.sub(r'[<>:"/\\|?*]', '_', new_filename_final).strip(" .")
        if not new_filename_final:
            base_fallback = f"renamed_{format_data['tmdbid'] if format_data['tmdbid'] != 'N/A' else 'unknown_id'}"
            if format_data.get('season') and format_data.get('episode'): base_fallback += f"_S{format_data['season']}E{format_data['episode']}"
            elif format_data.get('episode'): base_fallback += f"_E{format_data['episode']}"
            new_filename_final = base_fallback
            self.log_signal.emit(f"[WARNING] rename_video_file: 新文件名处理后为空, 使用回退: {new_filename_final}")
        self.log_signal.emit(f"[INFO] rename_video_file: 最终生成文件名 (无扩展名): '{new_filename_final}'")

        generated_folder_name = self.folder_format_template
        for k, v in format_data.items(): generated_folder_name = generated_folder_name.replace(f'{{{k}}}', str(v))
        generated_folder_name = re.sub(r'[<>:"/\\|?*]', '_', generated_folder_name).strip(" .")
        if not generated_folder_name:
            generated_folder_name = f"{format_data['title']}_{format_data['year']}"
            self.log_signal.emit(f"[WARNING] rename_video_file: 主文件夹名为空, 使用回退: {generated_folder_name}")
        self.log_signal.emit(f"[INFO] rename_video_file: 最终主文件夹名: '{generated_folder_name}'")

        generated_season_folder = ""
        if self.season_format_template.strip():
            should_create_season_folder = False
            if current_media_type == "tv" or self.custom_season_enabled:
                if format_data.get('season_int') or self.custom_season_enabled:
                    should_create_season_folder = True
                elif "{season}" not in self.season_format_template and "{season_int}" not in self.season_format_template:
                    should_create_season_folder = True
            elif "{season}" not in self.season_format_template and "{season_int}" not in self.season_format_template:
                should_create_season_folder = True


            if should_create_season_folder:
                generated_season_folder = self.season_format_template
                for k, v in format_data.items(): generated_season_folder = generated_season_folder.replace(f'{{{k}}}', str(v))
                generated_season_folder = re.sub(r'[<>:"/\\|?*]', '_', generated_season_folder).strip(" .")
                if not generated_season_folder and self.season_format_template.strip():
                    fallback_s_num = format_data['season_int'] if format_data.get('season_int') else "Misc"
                    generated_season_folder = f"Season_{fallback_s_num}"
                    self.log_signal.emit(f"[WARNING] rename_video_file: 季文件夹名为空(模板非空), 回退: {generated_season_folder}")
            else: self.log_signal.emit(f"[INFO] rename_video_file: 不创建季文件夹。")
        else: self.log_signal.emit(f"[INFO] rename_video_file: 季文件夹格式为空。")
        self.log_signal.emit(f"[INFO] rename_video_file: 最终季文件夹名: '{generated_season_folder}'")

        original_dir = os.path.dirname(video_os_path)
        new_base_folder_path = os.path.join(original_dir, generated_folder_name)
        new_folder_structure_path = os.path.join(new_base_folder_path, generated_season_folder) if generated_season_folder.strip() else new_base_folder_path
        new_filepath_final = os.path.join(new_folder_structure_path, f"{new_filename_final}{file_extension}")
        self.log_signal.emit(f"[INFO] rename_video_file: 最终完整新路径: '{new_filepath_final}'")

        # Emit preview signal first
        self.preview_signal.emit(os.path.basename(video_os_path), f"{new_filename_final}{file_extension}", generated_folder_name, generated_season_folder)

        if os.path.normpath(video_os_path) == os.path.normpath(new_filepath_final):
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] 文件 '{video_filename}' 无需重命名。")
            return None

        if self.preview_only:
            return (video_os_path, new_filepath_final)

        if os.path.exists(new_filepath_final):
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [ERROR] 目标 '{new_filepath_final}' 已存在。跳过。")
            return None
            
        try:
            os.makedirs(new_folder_structure_path, exist_ok=True)
            self.throttled_move(video_os_path, new_filepath_final)
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [SUCCESS] '{video_filename}' -> '{os.path.basename(new_filepath_final)}'")
            return (video_os_path, new_filepath_final)
        except Exception as e:
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [ERROR] 重命名/移动 '{video_filename}' 失败: {e}\n{traceback.format_exc()}")
            return None

    def rename_containing_folder(self, folder_path, representative_file_path, file_count):
        self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] rename_containing_folder: 开始处理文件夹: {folder_path}")
        
        recognition_name = os.path.basename(folder_path)
        self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [DEBUG] rename_containing_folder: 使用识别名称: '{recognition_name}'")

        title = "未知标题"
        year = "未知年份"
        tmdbid_for_naming = "N/A"
        category = None
        genres = []
        original_language = None
        origin_country = None
        current_media_type = "movie"

        if self.custom_tmdbid_enabled and self.custom_tmdbid_value and self.tmdb_api_key:
            tmdb_info_custom = self._fetch_tmdb_details_from_api(self.custom_tmdbid_value, self.tmdb_api_key, self.custom_tmdb_media_type)
            if tmdb_info_custom:
                title = tmdb_info_custom.get('title', title)
                year = tmdb_info_custom.get('year', year)
                tmdbid_for_naming = str(self.custom_tmdbid_value)
                current_media_type = self.custom_tmdb_media_type
                genres = tmdb_info_custom.get('genres', [])
                if current_media_type == "movie":
                    original_language = tmdb_info_custom.get('original_language')
                    production_countries = tmdb_info_custom.get('production_countries', [])
                    if production_countries: origin_country = production_countries[0]
                else:
                    origin_countries = tmdb_info_custom.get('origin_countries', [])
                    if origin_countries: origin_country = origin_countries[0]
                self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [DEBUG] rename_containing_folder: 已应用自定义TMDB信息: Title='{title}', Year='{year}', TMDBID='{tmdbid_for_naming}'")

        moviepilot_response = self.fetch_moviepilot_video_info(recognition_name, self.moviepilot_api_token)
        if moviepilot_response and moviepilot_response.get("success", False):
            api_data = moviepilot_response.get('data', {})
            media_info = api_data.get('media_info', {})
            
            if tmdbid_for_naming == "N/A":
                mp_type_str = media_info.get('type', "未知").lower()
                if "电视剧" in mp_type_str or "tv" in mp_type_str: current_media_type = "tv"
                elif "电影" in mp_type_str or "movie" in mp_type_str: current_media_type = "movie"

                title = str(media_info.get('title') or media_info.get('en_title') or "未知标题").strip()
                year = str(media_info.get('year') or "未知年份").strip()
                tmdbid_for_naming = str(media_info.get('tmdb_id') or "N/A").strip()
            
            if not category: category = media_info.get('category')
            if not genres: genres = media_info.get('genres', [])
            if not original_language: original_language = media_info.get('original_language')
            if not origin_country: origin_country = media_info.get('origin_country')
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [DEBUG] rename_containing_folder: MoviePilot数据已应用 (Title='{title}', Year='{year}', TMDBID='{tmdbid_for_naming}', Type='{current_media_type}')")
        else:
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [WARNING] rename_containing_folder: MoviePilot API未能获取 '{recognition_name}' 的有效信息。")

        category_folder = None
        if self.use_category_folders:
            category_folder = self._determine_category_folder(current_media_type, category, genres, original_language, origin_country)
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] rename_containing_folder: 确定的二级分类文件夹: {category_folder}")
        else:
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] rename_containing_folder: 二级分类文件夹功能已禁用")

        if year == "未知年份" or year == "0000" or not year.isdigit():
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [ERROR] rename_containing_folder: 无法确定年份，跳过文件夹 '{folder_path}'。")
            return None
        if title == "未知标题" or not title.strip():
            title = recognition_name

        new_folder_name = self.folder_rename_format.replace("{title}", title).replace("{year}", year)

        # 处理各种tmdbid占位符格式
        import re
        if tmdbid_for_naming != "N/A":
            # 支持多种tmdbid占位符格式：{tmdbid-{tmdbid}}, {tmdb-{tmdbid}}, {tmdbid}
            new_folder_name = new_folder_name.replace("{tmdbid-{tmdbid}}", f"{{tmdbid-{tmdbid_for_naming}}}")
            new_folder_name = new_folder_name.replace("{tmdb-{tmdbid}}", f"{{tmdbid-{tmdbid_for_naming}}}")
            new_folder_name = new_folder_name.replace("{tmdbid}", tmdbid_for_naming)
        else:
            # 移除所有tmdbid相关的占位符
            new_folder_name = new_folder_name.replace("{tmdbid-{tmdbid}}", "").strip()
            new_folder_name = new_folder_name.replace("{tmdb-{tmdbid}}", "").strip()
            new_folder_name = new_folder_name.replace("{tmdbid}", "").strip()

        new_folder_name = re.sub(r'[<>:"/\\|?*]', '_', new_folder_name).strip(" .")
        new_folder_name = re.sub(r'[\s\._-]+$', '', new_folder_name)
        new_folder_name = re.sub(r'([\s\._-])[\s\._]+', r'\1', new_folder_name)
        
        if not new_folder_name:
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [ERROR] rename_containing_folder: 生成的文件夹名为空，跳过 '{folder_path}'。")
            return None

        destination_base_dir = self.custom_dest_path if self.dest_mode == 'custom' else os.path.dirname(folder_path)
        
        # 构建目标路径，根据设置决定是否使用分类和年份
        if self.use_category_folders and category_folder and self.use_year_folders:
            new_folder_path = os.path.join(destination_base_dir, category_folder, year, new_folder_name)
        elif self.use_category_folders and category_folder:
            new_folder_path = os.path.join(destination_base_dir, category_folder, new_folder_name)
        elif self.use_year_folders:
            new_folder_path = os.path.join(destination_base_dir, year, new_folder_name)
        else:
            new_folder_path = os.path.join(destination_base_dir, new_folder_name)
        
        video_files = []
        files_by_season = {}
        if current_media_type == "tv":
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if os.path.splitext(file)[1].lower() in VIDEO_EXTENSIONS:
                        video_files.append(os.path.join(root, file))
            
            for video_path in video_files:
                season_number = self._get_season_number_for_file(video_path)
                if season_number not in files_by_season:
                    files_by_season[season_number] = []
                files_by_season[season_number].append(video_path)

        # Emit preview for the entire folder operation
        if current_media_type == "tv" and files_by_season:
            seasons_info = ", ".join([f"Season {s}" for s in sorted(files_by_season.keys())])
            category_info = f" [{category_folder}]" if category_folder and self.use_category_folders else ""
            preview_path = f"{new_folder_path}{category_info} ({seasons_info})"
            mode_info = f"电视剧 ({len(files_by_season)}个季)"
            self.preview_signal.emit(folder_path, preview_path, str(file_count), mode_info)
        else:
            category_info = f" [{category_folder}]" if category_folder and self.use_category_folders else ""
            preview_path = f"{new_folder_path}{category_info}"
            mode_info = f"电影 ({category_folder})" if category_folder and self.use_category_folders else "电影"
            self.preview_signal.emit(folder_path, preview_path, str(file_count), mode_info)
        
        if self.preview_only:
            return {"moved_files": 0, "moved_folders": 0}

        # --- EXECUTION ---
        files_moved_count = 0
        folders_moved_count = 0

        # Optimized move for the entire folder (typically for movies)
        if current_media_type == "movie":
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] 电影模式，尝试整体移动文件夹...")
            if os.path.normpath(folder_path) == os.path.normpath(new_folder_path):
                self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] 文件夹 '{os.path.basename(folder_path)}' 无需移动。")
                return {"moved_files": 0, "moved_folders": 0}

            if os.path.exists(new_folder_path):
                self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [WARNING] 目标路径 '{new_folder_path}' 已存在，跳过移动。")
                return None
            
            try:
                os.makedirs(os.path.dirname(new_folder_path), exist_ok=True)
                self.throttled_move(folder_path, new_folder_path)
                self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [SUCCESS] 移动文件夹 '{os.path.basename(folder_path)}' -> '{new_folder_name}'")
                # Count files inside for statistics
                moved_file_count = sum([len(files) for r, d, files in os.walk(new_folder_path)])
                return {"moved_files": moved_file_count, "moved_folders": 1}
            except Exception as e:
                self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [ERROR] 移动文件夹 '{os.path.basename(folder_path)}' 失败: {e}\n{traceback.format_exc()}")
                return None
        
        # File-by-file move for TV shows
        elif current_media_type == "tv":
            self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] 电视剧模式，开始逐个移动文件...")
            for season_number, season_files in files_by_season.items():
                season_folder_name = f"Season {season_number}"
                season_folder_path = os.path.join(new_folder_path, season_folder_name)
                if not os.path.exists(season_folder_path):
                    try:
                        os.makedirs(season_folder_path, exist_ok=True)
                    except OSError as e:
                        self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [ERROR] 创建季文件夹 '{season_folder_path}' 失败: {e}")
                        continue
                
                for file_path in season_files:
                    file_name = os.path.basename(file_path)
                    dest_file_path = os.path.join(season_folder_path, file_name)
                    if os.path.normpath(file_path) == os.path.normpath(dest_file_path):
                        continue
                    if os.path.exists(dest_file_path):
                        self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [WARNING] 目标文件已存在，跳过: '{dest_file_path}'")
                        continue
                    
                    try:
                        self.throttled_move(file_path, dest_file_path)
                        self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [SUCCESS] 移动 '{file_name}'")
                        files_moved_count += 1
                    except Exception as e:
                        self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [ERROR] 移动文件 '{file_name}' 时发生错误: {e}")

            # Cleanup
            try:
                if os.path.isdir(folder_path) and not os.listdir(folder_path):
                    os.rmdir(folder_path)
                    self.log_signal.emit(f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [INFO] 清理空的源文件夹: {folder_path}")
            except OSError:
                pass

        return {"moved_files": files_moved_count, "moved_folders": 0}

    def _get_season_number_for_file(self, video_path):
        self.log_signal.emit(f"[DEBUG] _get_season_number_for_file: 分析文件季度: {os.path.basename(video_path)}")
        
        if self.custom_season_enabled and self.custom_season_value:
            try:
                season_int = int(self.custom_season_value)
                self.log_signal.emit(f"[DEBUG] _get_season_number_for_file: 使用自定义季度: {season_int}")
                return season_int
            except ValueError:
                self.log_signal.emit(f"[WARNING] _get_season_number_for_file: 自定义季度值无效: {self.custom_season_value}")
        
        filename = os.path.basename(video_path)
        season_patterns = [
            r'[Ss](\d{1,2})[Ee]',
            r'Season[.\s_-]*(\d{1,2})',
            r'第(\d{1,2})季',
        ]
        
        for pattern in season_patterns:
            match = re.search(pattern, filename)
            if match:
                try:
                    season_int = int(match.group(1))
                    self.log_signal.emit(f"[DEBUG] _get_season_number_for_file: 从文件名提取季度: {season_int}")
                    return season_int
                except (ValueError, IndexError):
                    continue
        
        try:
            moviepilot_response = self.fetch_moviepilot_video_info(filename, self.moviepilot_api_token)
            if moviepilot_response and moviepilot_response.get("success", False):
                api_data = moviepilot_response.get('data', {})
                meta_info = api_data.get('meta_info', {})
                media_info = api_data.get('media_info', {})
                
                season_episode_str = meta_info.get('season_episode')
                if season_episode_str:
                    match_se = re.match(r'S(\d+)\s*E(\d+)', season_episode_str, re.IGNORECASE)
                    if match_se:
                        season_number_raw = match_se.group(1)
                        try:
                            season_int = int(season_number_raw)
                            self.log_signal.emit(f"[DEBUG] _get_season_number_for_file: 从MoviePilot API提取季度: {season_int}")
                            return season_int
                        except ValueError:
                            pass
                
                if media_info.get('season_info'):
                    first_season_info = media_info['season_info'][0] if isinstance(media_info['season_info'], list) and media_info['season_info'] else None
                    if first_season_info and first_season_info.get('season_number') is not None:
                        try:
                            season_int = int(first_season_info.get('season_number'))
                            self.log_signal.emit(f"[DEBUG] _get_season_number_for_file: 从MoviePilot season_info提取季度: {season_int}")
                            return season_int
                        except ValueError:
                            pass
        except Exception as e:
            self.log_signal.emit(f"[ERROR] _get_season_number_for_file: 从MoviePilot获取季度信息时出错: {e}")
        
        self.log_signal.emit(f"[WARNING] _get_season_number_for_file: 无法确定季度，默认为第1季")
        return 1

    def _determine_category_folder(self, media_type, category, genres, original_language, origin_country):
        self.log_signal.emit(f"[DEBUG] _determine_category_folder: 媒体类型={media_type}, 分类={category}, 类型={genres}, 语言={original_language}, 原产国={origin_country}")
        
        if category:
            self.log_signal.emit(f"[DEBUG] _determine_category_folder: 使用MoviePilot提供的分类: {category}")
            return category
        
        genre_ids = []
        if genres:
            for genre in genres:
                if isinstance(genre, (int, str)) and str(genre).isdigit():
                    genre_ids.append(str(genre))
                elif isinstance(genre, dict) and "id" in genre:
                    genre_ids.append(str(genre["id"]))
        
        origin_countries = []
        if origin_country:
            if isinstance(origin_country, str):
                if "," in origin_country:
                    origin_countries = [c.strip().upper() for c in origin_country.split(",") if c.strip()]
                else:
                    origin_countries = [origin_country.upper()]
            elif isinstance(origin_country, list):
                origin_countries = [c.upper() for c in origin_country]
        
        if media_type == "movie":
            if "16" in genre_ids:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到动画电影 (genre_ids包含16)")
                return "动画电影"
            if original_language and original_language.lower() in ["zh", "cn", "bo", "za"]:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到华语电影 (语言={original_language})")
                return "华语电影"
            if original_language and original_language.lower() in ["ja", "ko", "th"]:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到日韩电影 (语言={original_language})")
                return "日韩电影"
            self.log_signal.emit(f"[DEBUG] _determine_category_folder: 默认匹配到欧美电影")
            return "欧美电影"
                    
        else:
            if "16" in genre_ids and any(country in ["CN", "TW", "HK"] for country in origin_countries):
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到国漫 (genre_ids=16, 国家={origin_countries})")
                return "国漫"
            if "16" in genre_ids and "JP" in origin_countries:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到日番 (genre_ids=16, 国家=JP)")
                return "日番"
            if "10762" in genre_ids:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到儿童 (genre_ids=10762)")
                return "儿童"
            if "99" in genre_ids:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到纪录片 (genre_ids=99)")
                return "纪录片"
            if "10764" in genre_ids or "10767" in genre_ids:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到综艺 (genre_ids=10764或10767)")
                return "综艺"
            if "CN" in origin_countries:
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到国产剧 (国家=CN)")
                return "国产剧"
            if any(country in ["TW", "HK"] for country in origin_countries):
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到港台剧集 (国家=TW或HK)")
                return "港台剧集"
            if any(country in ["JP", "KR", "KP", "TH", "IN", "SG"] for country in origin_countries):
                self.log_signal.emit(f"[DEBUG] _determine_category_folder: 匹配到日韩剧 (国家=JP,KR,KP,TH,IN,SG之一)")
                return "日韩剧"
            self.log_signal.emit(f"[DEBUG] _determine_category_folder: 默认匹配到欧美剧")
            return "欧美剧"
        
        self.log_signal.emit(f"[WARNING] _determine_category_folder: 无法确定二级分类")
        return None


class VideoRenamerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("剧集视频文件重命名工具 (MoviePilot版) - PyQt6")
        self.setGeometry(100, 100, 1350, 850)

        if getattr(sys, 'frozen', False):
            application_path = os.path.dirname(sys.executable)
        else:
            application_path = os.path.dirname(os.path.abspath(__file__))

        config_filename = 'VideoRenamer_Qt6.ini'
        config_path = os.path.join(application_path, config_filename)

        print(f"Debug: Config path will be: {config_path}")

        self.settings = QSettings(config_path, QSettings.Format.IniFormat)
        self.dark_style_sheet = DARK_STYLE
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout()
        self.central_widget.setLayout(self.main_layout)
        self.log_file_handle = None
        self.init_ui()
        self.load_settings()
        self.worker = None

    def init_ui(self):
        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget)
        self.main_tab = QWidget()
        self.tab_widget.addTab(self.main_tab, "主界面")
        self.init_main_tab()
        self.settings_tab = QWidget()
        self.tab_widget.addTab(self.settings_tab, "设置")
        self.init_settings_tab()

    def init_main_tab(self):
        main_tab_layout = QVBoxLayout(self.main_tab)

        # Main vertical splitter for resizable height
        v_splitter = QSplitter(Qt.Orientation.Vertical)

        self.top_splitter = QSplitter(Qt.Orientation.Horizontal) 

        left_pane_widget = QWidget()
        left_pane_layout = QVBoxLayout(left_pane_widget)
        left_pane_layout.setContentsMargins(0,0,0,0)
        file_group = QGroupBox("文件/文件夹选择")
        file_layout = QVBoxLayout()
        self.file_list = QTextEdit()
        self.file_list.setPlaceholderText("拖放剧集文件或文件夹到这里...")
        self.file_list.setAcceptDrops(True)
        btn_layout = QHBoxLayout()
        self.browse_files_btn = QPushButton("浏览文件")
        self.browse_files_btn.clicked.connect(self.browse_files)
        self.browse_folder_btn = QPushButton("浏览文件夹")
        self.browse_folder_btn.clicked.connect(self.browse_folder)
        self.clear_btn = QPushButton("清空列表")
        self.clear_btn.clicked.connect(self.clear_file_list)
        btn_layout.addWidget(self.browse_files_btn)
        btn_layout.addWidget(self.browse_folder_btn)
        btn_layout.addWidget(self.clear_btn)
        file_layout.addWidget(self.file_list)
        file_layout.addLayout(btn_layout)
        file_group.setLayout(file_layout)
        left_pane_layout.addWidget(file_group)
        self.top_splitter.addWidget(left_pane_widget)

        right_pane_widget = QWidget()
        right_pane_layout = QVBoxLayout(right_pane_widget)
        right_pane_layout.setContentsMargins(0,0,0,0)
        custom_options_group = QGroupBox("自定义选项")
        custom_options_form_layout = QFormLayout()

        self.custom_season_checkbox = QCheckBox("自定义季度")
        self.custom_season_input = QLineEdit()
        self.custom_season_input.setPlaceholderText("例如: 1 或 01")
        self.custom_season_input.setEnabled(False)
        self.custom_season_checkbox.toggled.connect(self.custom_season_input.setEnabled)
        custom_options_form_layout.addRow(self.custom_season_checkbox, self.custom_season_input)

        self.custom_episode_offset_checkbox = QCheckBox("自定义集数偏移")
        self.custom_episode_offset_input = QLineEdit()
        self.custom_episode_offset_input.setPlaceholderText("整数偏移, 如 1, -2, 0")
        self.custom_episode_offset_input.setEnabled(False)
        self.custom_episode_offset_checkbox.toggled.connect(self.custom_episode_offset_input.setEnabled)
        custom_options_form_layout.addRow(self.custom_episode_offset_checkbox, self.custom_episode_offset_input)

        self.custom_tmdbid_checkbox = QCheckBox("自定义TMDBID")
        tmdb_id_line_layout = QHBoxLayout()
        self.custom_tmdbid_input = QLineEdit()
        self.custom_tmdbid_input.setPlaceholderText("输入TMDB数字ID")
        self.custom_tmdbid_input.setEnabled(False)
        tmdb_id_line_layout.addWidget(self.custom_tmdbid_input, 2)
        self.custom_tmdb_media_type_combo = QComboBox()
        self.custom_tmdb_media_type_combo.addItem("剧集 (TV)", "tv")
        self.custom_tmdb_media_type_combo.addItem("电影 (Movie)", "movie")
        self.custom_tmdb_media_type_combo.setEnabled(False)
        tmdb_id_line_layout.addWidget(self.custom_tmdb_media_type_combo, 1)
        custom_options_form_layout.addRow(self.custom_tmdbid_checkbox, tmdb_id_line_layout)
        self.fetch_tmdb_info_button = QPushButton("获取TMDB信息")
        self.fetch_tmdb_info_button.setEnabled(False)
        self.fetch_tmdb_info_button.clicked.connect(self.fetch_tmdb_data_for_ui_display)
        custom_options_form_layout.addRow(self.fetch_tmdb_info_button)
        self.custom_tmdbid_checkbox.toggled.connect(self.custom_tmdbid_input.setEnabled)
        self.custom_tmdbid_checkbox.toggled.connect(self.custom_tmdb_media_type_combo.setEnabled)
        self.custom_tmdbid_checkbox.toggled.connect(self.fetch_tmdb_info_button.setEnabled)
        self.tmdb_title_label = QLabel("TMDB标题: N/A")
        self.tmdb_year_label = QLabel("TMDB年份: N/A")
        custom_options_form_layout.addRow(self.tmdb_title_label)
        custom_options_form_layout.addRow(self.tmdb_year_label)
        custom_options_group.setLayout(custom_options_form_layout)
        right_pane_layout.addWidget(custom_options_group)
        right_pane_layout.addStretch(1)
        self.top_splitter.addWidget(right_pane_widget)

        self.top_splitter.setSizes([int(self.width() * 0.5), int(self.width() * 0.5)])
        
        v_splitter.addWidget(self.top_splitter)

        preview_group = QGroupBox("预览/结果")
        preview_layout = QHBoxLayout()
        self.preview_table = QTableWidget()
        self.preview_table.setColumnCount(4)
        self.preview_table.setHorizontalHeaderLabels(["原文件名", "新文件名", "目标主文件夹", "目标季文件夹"])
        self.preview_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.preview_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.preview_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        self.preview_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.preview_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        preview_layout.addWidget(self.preview_table)
        preview_group.setLayout(preview_layout)
        v_splitter.addWidget(preview_group)

        progress_group = QGroupBox("日志与进度")
        progress_layout = QVBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setPlaceholderText("操作日志将显示在这里...")
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.log_output)
        progress_group.setLayout(progress_layout)
        v_splitter.addWidget(progress_group)

        # Set initial sizes for the vertical splitter
        v_splitter.setSizes([int(self.height() * 0.35), int(self.height() * 0.4), int(self.height() * 0.25)])

        main_tab_layout.addWidget(v_splitter)

        action_layout = QHBoxLayout()
        self.preview_btn = QPushButton("预览重命名")
        self.preview_btn.clicked.connect(lambda: self.start_processing(preview_only=True))
        self.execute_btn = QPushButton("执行重命名")
        self.execute_btn.clicked.connect(lambda: self.start_processing(preview_only=False))
        self.execute_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.cancel_processing)
        self.cancel_btn.setEnabled(False)
        action_layout.addWidget(self.preview_btn)
        action_layout.addWidget(self.execute_btn)
        action_layout.addWidget(self.cancel_btn)
        main_tab_layout.addLayout(action_layout)
        self.setAcceptDrops(True)

    def fetch_tmdb_data_for_ui_display(self):
        tmdb_id = self.custom_tmdbid_input.text().strip()
        api_key = self.tmdb_api_key_input.text().strip()
        media_type = self.custom_tmdb_media_type_combo.currentData()

        if not tmdb_id:
            QMessageBox.warning(self, "输入错误", "请输入TMDB ID。")
            return
        if not api_key:
            QMessageBox.warning(self, "API Key错误", "请在设置中填写TMDB API Key。")
            self.tab_widget.setCurrentIndex(1)
            return

        self.write_to_log_display_and_file(f"[INFO] GUI.fetch_tmdb_data_for_ui_display: 正在获取TMDB ID {tmdb_id} (类型: {media_type.upper()}) 的信息...")
        QApplication.processEvents()
        fetched_info = self._fetch_tmdb_details_gui(tmdb_id, api_key, media_type)
        if fetched_info:
            self.tmdb_title_label.setText(f"TMDB标题: {fetched_info.get('title', 'N/A')}")
            self.tmdb_year_label.setText(f"TMDB年份: {fetched_info.get('year', 'N/A')}")
            self.write_to_log_display_and_file(f"[INFO] GUI.fetch_tmdb_data_for_ui_display: TMDB信息获取成功 - {fetched_info.get('title', '')} ({fetched_info.get('year', '')})")
        else:
            self.tmdb_title_label.setText("TMDB标题: 获取失败")
            self.tmdb_year_label.setText("TMDB年份: 获取失败")
            self.write_to_log_display_and_file(f"[WARNING] GUI.fetch_tmdb_data_for_ui_display: 获取TMDB ID {tmdb_id} (类型: {media_type.upper()}) 信息失败。")
            QMessageBox.warning(self, "TMDB获取失败", f"无法从TMDB获取ID {tmdb_id} (类型: {media_type.upper()}) 的信息。请检查ID、类型和API Key。")

    def _fetch_tmdb_details_gui(self, tmdb_id, api_key, media_type="tv"):
        self.write_to_log_display_and_file(f"[DEBUG] GUI._fetch_tmdb_details_gui: 请求TMDB ID={tmdb_id}, Type={media_type}")
        if media_type not in ["tv", "movie"]: media_type = "tv"
        url = f"{TMDB_API_BASE_URL}/{media_type}/{tmdb_id}?api_key={api_key}&language=zh-CN"
        self.write_to_log_display_and_file(f"[DEBUG] GUI._fetch_tmdb_details_gui: 请求URL (api_key省略): {TMDB_API_BASE_URL}/{media_type}/{tmdb_id}?api_key=***&language=zh-CN")
        data_to_return = {}
        try:
            response = requests.get(url, timeout=7)
            self.write_to_log_display_and_file(f"[DEBUG] GUI._fetch_tmdb_details_gui: 响应状态码: {response.status_code}")
            response.raise_for_status()
            tmdb_data = response.json()
            if media_type == "tv":
                data_to_return['title'] = tmdb_data.get('name', tmdb_data.get('original_name', '未知'))
                air_date = tmdb_data.get('first_air_date', '')
                data_to_return['year'] = air_date.split('-')[0] if air_date else '未知'
            elif media_type == "movie":
                data_to_return['title'] = tmdb_data.get('title', tmdb_data.get('original_title', '未知'))
                release_date = tmdb_data.get('release_date', '')
                data_to_return['year'] = release_date.split('-')[0] if release_date else '未知'
            return data_to_return
        except requests.exceptions.HTTPError as e:
             self.write_to_log_display_and_file(f"[ERROR] GUI._fetch_tmdb_details_gui: TMDB API HTTP错误 (UI Fetch, {media_type.upper()} ID {tmdb_id}): {e.response.status_code}")
        except requests.exceptions.RequestException as e:
            self.write_to_log_display_and_file(f"[ERROR] GUI._fetch_tmdb_details_gui: TMDB API请求错误 (UI Fetch, {media_type.upper()} ID {tmdb_id}): {str(e)}")
        except Exception as e:
            self.write_to_log_display_and_file(f"[ERROR] GUI._fetch_tmdb_details_gui: 解析TMDB API响应时出错 (UI Fetch, {media_type.upper()} ID {tmdb_id}): {str(e)}\n{traceback.format_exc()}")
        return None

    def init_settings_tab(self):
        layout = QVBoxLayout(self.settings_tab)
        
        folder_org_group = QGroupBox("文件夹整理模式")
        folder_org_layout = QVBoxLayout()

        self.folder_rename_only_checkbox = QCheckBox("启用文件夹整理模式（仅移动和重命名文件夹，不修改文件名）")
        folder_org_layout.addWidget(self.folder_rename_only_checkbox)

        self.folder_format_widget = QWidget()
        folder_format_layout = QFormLayout(self.folder_format_widget)
        self.folder_rename_format_input = QLineEdit()
        folder_format_layout.addRow("文件夹名称格式:", self.folder_rename_format_input)
        folder_org_layout.addWidget(self.folder_format_widget)
        
        # 添加文件夹结构选项
        folder_structure_group = QGroupBox("文件夹结构选项")
        folder_structure_layout = QVBoxLayout()
        
        self.use_category_folders_checkbox = QCheckBox("使用二级分类文件夹 (如：欧美电影、日韩剧等)")
        self.use_year_folders_checkbox = QCheckBox("使用年份文件夹")
        
        folder_structure_layout.addWidget(self.use_category_folders_checkbox)
        folder_structure_layout.addWidget(self.use_year_folders_checkbox)
        folder_structure_group.setLayout(folder_structure_layout)
        folder_org_layout.addWidget(folder_structure_group)

        self.dest_group = QGroupBox("目标目录设置")
        dest_layout = QVBoxLayout()
        self.dest_radio_current = QRadioButton("在源文件夹位置进行整理（保持原位置）")
        self.dest_radio_custom = QRadioButton("移动到指定的目标根目录下整理")
        
        custom_dest_layout = QHBoxLayout()
        self.custom_dest_path_edit = QLineEdit()
        self.custom_dest_path_edit.setPlaceholderText("选择一个用于存放年份文件夹的根目录")
        self.browse_dest_path_btn = QPushButton("浏览...")
        self.browse_dest_path_btn.clicked.connect(self.browse_dest_folder)
        custom_dest_layout.addWidget(self.custom_dest_path_edit)
        custom_dest_layout.addWidget(self.browse_dest_path_btn)

        dest_layout.addWidget(self.dest_radio_current)
        dest_layout.addWidget(self.dest_radio_custom)
        dest_layout.addLayout(custom_dest_layout)
        self.dest_group.setLayout(dest_layout)
        folder_org_layout.addWidget(self.dest_group)

        self.folder_rename_only_checkbox.toggled.connect(self.update_folder_mode_ui)
        self.dest_radio_custom.toggled.connect(lambda checked: self.custom_dest_path_edit.setEnabled(checked))
        self.dest_radio_custom.toggled.connect(lambda checked: self.browse_dest_path_btn.setEnabled(checked))

        folder_org_group.setLayout(folder_org_layout)
        layout.addWidget(folder_org_group)

        api_group = QGroupBox("API设置")
        api_layout = QFormLayout()
        self.moviepilot_api_server_input = QLineEdit()
        self.moviepilot_api_server_input.setPlaceholderText("例如: http://moviepilot.example.com:8080")
        api_layout.addRow("MoviePilot API服务器:", self.moviepilot_api_server_input)
        self.moviepilot_api_token_input = QLineEdit()
        self.moviepilot_api_token_input.setPlaceholderText("例如: moviepilot_token_string")
        api_layout.addRow("MoviePilot API Token:", self.moviepilot_api_token_input)

        self.tmdb_api_key_input = QLineEdit()
        self.tmdb_api_key_input.setPlaceholderText("输入你的TMDB API v3 Key")
        api_layout.addRow("TMDB API Key (v3 Auth):", self.tmdb_api_key_input)
        api_group.setLayout(api_layout)
        layout.addWidget(api_group)

        format_group = QGroupBox("重命名格式")
        format_layout = QFormLayout()
        self.rename_format_combo = QComboBox()
        self.rename_format_combo.addItems([
            "{title} - S{season}E{episode} - {filename}",
            "{title}.S{season}E{episode}",
            "{title}.({year}).S{season}E{episode}",
            "S{season}E{episode}.{title}",
            "{filename} S{season}E{episode}"
        ])
        self.rename_format_combo.setEditable(True)
        format_layout.addRow("文件名格式:", self.rename_format_combo)
        self.folder_format_input = QLineEdit()
        self.folder_format_input.setText("({year}){title}[tmdbid={tmdbid}]")
        format_layout.addRow("主文件夹格式:", self.folder_format_input)
        self.season_format_input = QLineEdit()
        self.season_format_input.setText("Season {season_int}")
        format_layout.addRow("季文件夹格式:", self.season_format_input)
        self.show_placeholders_btn = QPushButton("查看/复制可用占位符说明")
        self.show_placeholders_btn.clicked.connect(self.show_placeholder_info_dialog)
        format_layout.addRow(self.show_placeholders_btn)
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)

        regex_group = QGroupBox("正则替换规则 (应用于新文件名)")
        regex_layout = QVBoxLayout()
        self.regex_rules_edit = QPlainTextEdit()
        self.regex_rules_edit.setPlaceholderText("例如:\n(?i) unwanted => replacement\n\\[[^\\]]*\\] => (移除方括号内容)")
        regex_layout.addWidget(self.regex_rules_edit)
        regex_group.setLayout(regex_layout)
        layout.addWidget(regex_group)

        log_file_group = QGroupBox("详细日志记录")
        log_file_layout = QFormLayout()
        self.enable_file_log_checkbox = QCheckBox("启用日志文件记录")
        self.enable_file_log_checkbox.toggled.connect(self._toggle_log_file_widgets)
        log_file_layout.addRow(self.enable_file_log_checkbox)
        log_path_layout = QHBoxLayout()
        self.log_file_path_edit = QLineEdit()
        self.log_file_path_edit.setReadOnly(True)
        self.log_file_path_edit.setPlaceholderText("未选择日志文件")
        self.browse_log_file_btn = QPushButton("浏览...")
        self.browse_log_file_btn.clicked.connect(self.browse_log_file_location)
        log_path_layout.addWidget(self.log_file_path_edit)
        log_path_layout.addWidget(self.browse_log_file_btn)
        log_file_layout.addRow("日志文件路径:", log_path_layout)
        log_file_group.setLayout(log_file_layout)
        layout.addWidget(log_file_group)
        self._toggle_log_file_widgets(False)
        
        # Add UI settings group for dark mode
        misc_group = QGroupBox("界面设置")
        misc_layout = QVBoxLayout()
        self.dark_mode_checkbox = QCheckBox("启用夜间模式")
        self.dark_mode_checkbox.toggled.connect(self.toggle_dark_mode)
        misc_layout.addWidget(self.dark_mode_checkbox)
        misc_group.setLayout(misc_layout)
        layout.addWidget(misc_group)

        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings_manually)
        layout.addWidget(save_btn)
        layout.addStretch(1)

    def show_placeholder_info_dialog(self):
        placeholder_text = (
            "可用占位符 (主要信息来自 MoviePilot API 或自定义 TMDB):\n\n"
            "{title}: 标题 (来自TMDB自定义或MoviePilot识别)\n"
            "{year}: 年份 (来自TMDB自定义或MoviePilot识别)\n"
            "{tmdbid}: TMDB ID (来自TMDB自定义或MoviePilot识别)\n"
            "{season}: 两位数季号 (如 01, 10) (来自自定义季度或MoviePilot识别)\n"
            "{season_int}: 实际季号数字 (如 1, 10) (来自自定义季度或MoviePilot识别)\n"
            "{episode}: 两位数集号 (如 01, 15) (来自MoviePilot识别, 可受自定义集数偏移影响)\n"
            "{filename}: 原文件名(无扩展名)\n\n"
            "注意：\n"
            "- 自定义TMDBID会优先使用其获取的标题和年份。\n"
            "- 自定义季度会优先使用输入的季号。\n"
            "- 自定义集数偏移会调整最终的集号 (结果至少为1)。\n"
            "- 若使用自定义TMDBID指定为'电影'，且未自定义季度，则{season}/{episode}相关占位符可能为空或默认值(除非集数偏移提供了值)，文件名格式中对S/E的处理会自动调整。\n"
            "  如不希望电影有季文件夹，可将'季文件夹格式'设置为空。\n\n"
            "文件夹整理模式说明：\n"
            "- 启用「使用二级分类文件夹」可将文件按类型（如：欧美电影、日韩剧等）自动分类。\n"
            "- 启用「使用年份文件夹」可将文件按年份自动分类。\n"
            "- 文件夹结构示例：\n"
            "  - 全部启用：[目标目录]/[二级分类]/[年份]/[剧名]\n"
            "  - 仅启用分类：[目标目录]/[二级分类]/[剧名]\n"
            "  - 仅启用年份：[目标目录]/[年份]/[剧名]\n"
            "  - 全部禁用：[目标目录]/[剧名]"
        )
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("可用占位符说明")
        msg_box.setTextFormat(Qt.TextFormat.PlainText)
        msg_box.setText(placeholder_text)
        msg_box.setIcon(QMessageBox.Icon.Information)
        copy_button = msg_box.addButton("复制到剪贴板", QMessageBox.ButtonRole.ActionRole)
        msg_box.addButton(QMessageBox.StandardButton.Ok)
        msg_box.exec()
        if msg_box.clickedButton() == copy_button:
            QApplication.clipboard().setText(placeholder_text)
            self.write_to_log_display_and_file("[INFO] GUI.show_placeholder_info_dialog: 占位符说明已复制到剪贴板。")

    def _toggle_log_file_widgets(self, enabled):
        self.log_file_path_edit.setEnabled(enabled)
        self.browse_log_file_btn.setEnabled(enabled)
        if not enabled:
            if self.log_file_handle:
                self.write_to_log_display_and_file("[INFO] GUI._toggle_log_file_widgets: 禁用日志文件记录，关闭日志文件。")
                try:
                    self.log_file_handle.flush()
                    self.log_file_handle.close()
                except Exception as e:
                    print(f"关闭日志句柄错误: {e}")
                self.log_file_handle = None
        else: 
            log_path = self.log_file_path_edit.text()
            if log_path:
                self.write_to_log_display_and_file(f"[INFO] GUI._toggle_log_file_widgets: 启用日志文件记录，尝试打开: {log_path}")
                self._open_log_file(log_path)
            else:
                self.write_to_log_display_and_file("[WARNING] GUI._toggle_log_file_widgets: 启用日志文件记录但路径为空。")


    def browse_log_file_location(self):
        self.write_to_log_display_and_file("[DEBUG] GUI.browse_log_file_location: 打开文件保存对话框。")
        default_dir = os.path.dirname(self.log_file_path_edit.text()) if self.log_file_path_edit.text() else os.path.expanduser("~")
        default_filename = os.path.join(default_dir, f"ShowRenamer_Log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        
        filePath, _ = QFileDialog.getSaveFileName(self, "选择日志文件", default_filename, "文本文件 (*.txt);;所有文件 (*)")
        
        if filePath:
            self.write_to_log_display_and_file(f"[INFO] GUI.browse_log_file_location: 用户选择日志文件: {filePath}")
            self.log_file_path_edit.setText(filePath)
            self._open_log_file(filePath)
        else:
            self.write_to_log_display_and_file("[DEBUG] GUI.browse_log_file_location: 用户取消选择日志文件。")


    def _open_log_file(self, file_path):
        self.write_to_log_display_and_file(f"[DEBUG] GUI._open_log_file: 尝试打开日志文件: {file_path}")
        if self.log_file_handle and not self.log_file_handle.closed:
            self.write_to_log_display_and_file(f"[DEBUG] GUI._open_log_file: 先关闭已打开的日志文件句柄。")
            try:
                self.log_file_handle.flush()
                self.log_file_handle.close()
            except Exception as e:
                self.write_to_log_display_and_file(f"[ERROR] GUI._open_log_file: 关闭旧日志句柄时出错: {e}")
            self.log_file_handle = None

        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            self.log_file_handle = open(file_path, "a", encoding="utf-8")
            ts = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.log_file_handle.write(f"\n--- Log started at {ts} ---\n")
            self.log_file_handle.flush()
            self.write_to_log_display_and_file(f"[INFO] GUI._open_log_file: 详细日志已成功记录到: {file_path}")
        except Exception as e:
            self.write_to_log_display_and_file(f"[ERROR] GUI._open_log_file: 无法打开日志文件 '{file_path}': {e}")
            QMessageBox.warning(self, "日志文件错误", f"无法打开日志 '{file_path}': {e}\n文件日志将被禁用。")
            self.enable_file_log_checkbox.setChecked(False)
            self.log_file_handle = None


    def load_settings(self):
        self.write_to_log_display_and_file("[DEBUG] GUI.load_settings: 开始加载设置。")
        self.moviepilot_api_server_input.setText(self.settings.value("moviepilot_api_server", "http://192.168.50.12:3016"))
        self.moviepilot_api_token_input.setText(self.settings.value("moviepilot_api_token", ""))
        self.tmdb_api_key_input.setText(self.settings.value("tmdb_api_key", ""))
        default_rename = "{title} - S{season}E{episode} - {filename}"
        self.rename_format_combo.setCurrentText(self.settings.value("rename_format", default_rename))
        self.folder_format_input.setText(self.settings.value("folder_format", "({year}){title}[tmdbid={tmdbid}]"))
        self.season_format_input.setText(self.settings.value("season_format", "Season {season_int}"))
        self.regex_rules_edit.setPlainText(self.settings.value("regex_rules", ""))

        enable_log = self.settings.value("enable_file_log", False, type=bool) 
        self.enable_file_log_checkbox.setChecked(enable_log)

        log_path = self.settings.value("log_file_path", "")
        self.log_file_path_edit.setText(log_path)

        self.custom_season_checkbox.setChecked(self.settings.value("custom_season_enabled", False, type=bool))
        self.custom_tmdbid_checkbox.setChecked(self.settings.value("custom_tmdbid_enabled", False, type=bool))
        last_media_type = self.settings.value("custom_tmdb_media_type", "tv")
        index = self.custom_tmdb_media_type_combo.findData(last_media_type)
        if index != -1:
            self.custom_tmdb_media_type_combo.setCurrentIndex(index)

        self.custom_episode_offset_checkbox.setChecked(self.settings.value("custom_episode_offset_enabled", False, type=bool))
        self.custom_episode_offset_input.setText(self.settings.value("custom_episode_offset_value", "0"))
        
        self.folder_rename_only_checkbox.setChecked(self.settings.value("folder_rename_only_enabled", False, type=bool))
        self.folder_rename_format_input.setText(self.settings.value("folder_rename_format", "{title} ({year}) {tmdbid-{tmdbid}}"))
        self.dest_radio_current.setChecked(self.settings.value("dest_mode_is_current", True, type=bool))
        self.dest_radio_custom.setChecked(not self.settings.value("dest_mode_is_current", True, type=bool))
        self.custom_dest_path_edit.setText(self.settings.value("custom_dest_path", ""))
        
        # 加载新的文件夹结构选项
        self.use_category_folders_checkbox.setChecked(self.settings.value("use_category_folders", True, type=bool))
        self.use_year_folders_checkbox.setChecked(self.settings.value("use_year_folders", True, type=bool))

        self.custom_season_input.setEnabled(self.custom_season_checkbox.isChecked())
        self.custom_tmdbid_input.setEnabled(self.custom_tmdbid_checkbox.isChecked())
        self.custom_tmdb_media_type_combo.setEnabled(self.custom_tmdbid_checkbox.isChecked())
        self.fetch_tmdb_info_button.setEnabled(self.custom_tmdbid_checkbox.isChecked())
        self.custom_episode_offset_input.setEnabled(self.custom_episode_offset_checkbox.isChecked())
        
        is_folder_mode_enabled = self.folder_rename_only_checkbox.isChecked()
        self.update_folder_mode_ui(is_folder_mode_enabled)

        self._toggle_log_file_widgets(self.enable_file_log_checkbox.isChecked())

        self.write_to_log_display_and_file("[DEBUG] GUI.load_settings: 设置加载完成。")

        # Load dark mode setting
        is_dark_mode = self.settings.value("dark_mode_enabled", False, type=bool)
        self.dark_mode_checkbox.setChecked(is_dark_mode)
        # Apply theme on startup without logging to avoid premature log entries
        if is_dark_mode:
            self.setStyleSheet(self.dark_style_sheet)

        self.custom_season_input.setEnabled(self.custom_season_checkbox.isChecked())
        self.custom_tmdbid_input.setEnabled(self.custom_tmdbid_checkbox.isChecked())
        self.custom_tmdb_media_type_combo.setEnabled(self.custom_tmdbid_checkbox.isChecked())

    def _save_settings_to_ini(self):
        self.write_to_log_display_and_file("[DEBUG] GUI._save_settings_to_ini: 开始保存设置到INI文件。")
        self.settings.setValue("moviepilot_api_server", self.moviepilot_api_server_input.text().strip())
        self.settings.setValue("moviepilot_api_token", self.moviepilot_api_token_input.text().strip())
        self.settings.setValue("tmdb_api_key", self.tmdb_api_key_input.text().strip())
        self.settings.setValue("rename_format", self.rename_format_combo.currentText().strip())
        self.settings.setValue("folder_format", self.folder_format_input.text().strip())
        self.settings.setValue("season_format", self.season_format_input.text().strip())
        self.settings.setValue("regex_rules", self.regex_rules_edit.toPlainText())
        
        self.settings.setValue("enable_file_log", self.enable_file_log_checkbox.isChecked())
        self.settings.setValue("log_file_path", self.log_file_path_edit.text())

        self.settings.setValue("custom_season_enabled", self.custom_season_checkbox.isChecked())
        self.settings.setValue("custom_tmdbid_enabled", self.custom_tmdbid_checkbox.isChecked())
        self.settings.setValue("custom_tmdb_media_type", self.custom_tmdb_media_type_combo.currentData())

        self.settings.setValue("custom_episode_offset_enabled", self.custom_episode_offset_checkbox.isChecked())
        self.settings.setValue("custom_episode_offset_value", self.custom_episode_offset_input.text().strip())

        self.settings.setValue("folder_rename_only_enabled", self.folder_rename_only_checkbox.isChecked())
        self.settings.setValue("folder_rename_format", self.folder_rename_format_input.text().strip())
        self.settings.setValue("dest_mode_is_current", self.dest_radio_current.isChecked())
        self.settings.setValue("custom_dest_path", self.custom_dest_path_edit.text().strip())
        
        # 保存新的文件夹结构选项
        self.settings.setValue("use_category_folders", self.use_category_folders_checkbox.isChecked())
        self.settings.setValue("use_year_folders", self.use_year_folders_checkbox.isChecked())

        # Save dark mode setting
        self.settings.setValue("dark_mode_enabled", self.dark_mode_checkbox.isChecked())

        self.settings.sync()
        self.write_to_log_display_and_file("[DEBUG] GUI._save_settings_to_ini: 设置已同步到INI文件。")


    def save_settings_manually(self):
        self.write_to_log_display_and_file("[INFO] GUI.save_settings_manually: 用户点击保存设置。")
        
        old_log_path = self.settings.value("log_file_path", "") 
        old_enable_status = self.settings.value("enable_file_log", False, type=bool)

        self._save_settings_to_ini()

        new_log_path = self.log_file_path_edit.text()
        new_enable_status = self.enable_file_log_checkbox.isChecked()

        if new_enable_status:
            if old_log_path != new_log_path or not old_enable_status:
                self.write_to_log_display_and_file("[DEBUG] GUI.save_settings_manually: 日志路径或启用状态已更改，重新打开日志文件。")
                self._open_log_file(new_log_path)
        elif not new_enable_status and old_enable_status:
            if self.log_file_handle and not self.log_file_handle.closed:
                self.write_to_log_display_and_file("[DEBUG] GUI.save_settings_manually: 日志已禁用，关闭文件句柄。")
                try:
                    self.log_file_handle.flush()
                    self.log_file_handle.close()
                except Exception as e:
                    self.write_to_log_display_and_file(f"[ERROR] GUI.save_settings_manually: 关闭日志句柄时出错: {e}")
                self.log_file_handle = None
                self.write_to_log_display_and_file("[INFO] GUI.save_settings_manually: 详细日志文件记录已禁用。")
        
        QMessageBox.information(self, "成功", "设置已保存！")
        self.write_to_log_display_and_file("[INFO] GUI.save_settings_manually: 设置保存成功消息框已显示。")


    def _add_paths_to_list(self, paths_to_add: list[str]):
        if not paths_to_add:
            self.write_to_log_display_and_file("[DEBUG] GUI._add_paths_to_list: 无路径可添加。")
            return

        self.write_to_log_display_and_file(f"[DEBUG] GUI._add_paths_to_list: 准备添加路径: {paths_to_add}")
        current_text = self.file_list.toPlainText()
        current_files_set = set()
        for line in current_text.splitlines():
            stripped_line = line.strip()
            if stripped_line:
                qurl_temp = QUrl(stripped_line)
                if qurl_temp.isLocalFile():
                    current_files_set.add(os.path.normpath(qurl_temp.toLocalFile().lower()))
                else:
                    current_files_set.add(os.path.normpath(stripped_line.lower()))


        self.write_to_log_display_and_file(f"[DEBUG] GUI._add_paths_to_list: 当前列表中的文件数量 (规范化集合): {len(current_files_set)}")

        new_files_to_append_to_gui = []
        for p_raw in paths_to_add:
            p_cleaned_for_gui = p_raw.strip()
            
            norm_p_compare_key = ""
            qurl_p = QUrl(p_cleaned_for_gui)
            if qurl_p.isLocalFile():
                norm_p_compare_key = os.path.normpath(qurl_p.toLocalFile().lower())
            else:
                norm_p_compare_key = os.path.normpath(p_cleaned_for_gui.lower())


            if norm_p_compare_key not in current_files_set:
                new_files_to_append_to_gui.append(p_cleaned_for_gui)
                current_files_set.add(norm_p_compare_key)
                self.write_to_log_display_and_file(f"[DEBUG] GUI._add_paths_to_list: 准备添加新路径: '{p_cleaned_for_gui}' (规范化键: '{norm_p_compare_key}')")
            else:
                self.write_to_log_display_and_file(f"[DEBUG] GUI._add_paths_to_list: 路径 '{p_cleaned_for_gui}' (规范化键 '{norm_p_compare_key}') 已存在于列表中。")
        
        if new_files_to_append_to_gui:
            self.write_to_log_display_and_file(f"[INFO] GUI._add_paths_to_list: 添加了 {len(new_files_to_append_to_gui)} 个新路径到列表。")
            prefix = "\n" if current_text.strip() else ""
            self.file_list.append(prefix + "\n".join(new_files_to_append_to_gui))
            self.preview_table.setRowCount(0)
        elif paths_to_add:
            self.write_to_log_display_and_file("[INFO] GUI._add_paths_to_list: 所选路径已在列表中或为空。")


    def _scan_folder_for_videos(self, folder_path: str) -> List[str]:
        """优化的文件夹视频扫描方法"""
        self.write_to_log_display_and_file(f"[DEBUG] GUI._scan_folder_for_videos: 开始扫描文件夹: {folder_path}")

        if not os.path.isdir(folder_path):
            self.write_to_log_display_and_file(f"[ERROR] GUI._scan_folder_for_videos: '{folder_path}' 不是有效文件夹。")
            return []

        video_files = []
        try:
            # 使用os.walk进行递归扫描，比手动管理队列更高效
            for root, dirs, files in os.walk(folder_path):
                # 过滤隐藏目录和系统目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['System Volume Information', '$RECYCLE.BIN']]

                # 并行处理文件检查
                video_files_in_dir = self._filter_video_files_parallel(root, files)
                video_files.extend(video_files_in_dir)

                # 定期更新UI，避免界面冻结
                if len(video_files) % 100 == 0:
                    QApplication.processEvents()

            self.write_to_log_display_and_file(f"[DEBUG] GUI._scan_folder_for_videos: 扫描完成，找到 {len(video_files)} 个视频文件。")

        except Exception as e:
            self.write_to_log_display_and_file(f"[ERROR] GUI._scan_folder_for_videos: 扫描文件夹出错: {e}\n{traceback.format_exc()}")

        return video_files

    def _filter_video_files_parallel(self, root_path: str, files: List[str]) -> List[str]:
        """并行过滤视频文件"""
        video_files = []

        # 对于文件数量较少的目录，直接处理
        if len(files) <= 20:
            for file in files:
                if os.path.splitext(file)[1].lower() in VIDEO_EXTENSIONS:
                    video_files.append(os.path.join(root_path, file))
            return video_files

        # 对于文件数量较多的目录，使用线程池
        def check_video_file(file):
            if os.path.splitext(file)[1].lower() in VIDEO_EXTENSIONS:
                return os.path.join(root_path, file)
            return None

        try:
            with ThreadPoolExecutor(max_workers=min(8, len(files))) as executor:
                futures = [executor.submit(check_video_file, file) for file in files]
                for future in as_completed(futures):
                    result = future.result()
                    if result:
                        video_files.append(result)
        except Exception as e:
            self.write_to_log_display_and_file(f"[ERROR] _filter_video_files_parallel: 并行过滤出错: {e}")
            # 回退到串行处理
            for file in files:
                if os.path.splitext(file)[1].lower() in VIDEO_EXTENSIONS:
                    video_files.append(os.path.join(root_path, file))

        return video_files


    def browse_files(self):
        self.write_to_log_display_and_file("[DEBUG] GUI.browse_files: 打开文件选择对话框。")
        last_dir = self.settings.value("last_browse_dir", os.path.expanduser("~"))
        self.write_to_log_display_and_file(f"[DEBUG] GUI.browse_files: 上次浏览目录: {last_dir}")
        
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择视频文件", last_dir,
            f"视频文件 ({' '.join(['*' + ext for ext in VIDEO_EXTENSIONS])});;所有文件 (*.*)"
        )
        if files:
            self.write_to_log_display_and_file(f"[INFO] GUI.browse_files: 用户选择了 {len(files)} 个文件: {files}")
            self._add_paths_to_list(files)
            if files:
                new_last_dir = os.path.dirname(files[0])
                self.settings.setValue("last_browse_dir", new_last_dir)
                self.write_to_log_display_and_file(f"[DEBUG] GUI.browse_files: 更新上次浏览目录为: {new_last_dir}")
        else:
            self.write_to_log_display_and_file("[DEBUG] GUI.browse_files: 用户未选择文件。")


    def browse_folder(self):
        self.write_to_log_display_and_file("[DEBUG] GUI.browse_folder: 打开文件夹选择对话框。")
        last_dir = self.settings.value("last_browse_dir", os.path.expanduser("~"))
        self.write_to_log_display_and_file(f"[DEBUG] GUI.browse_folder: 上次浏览目录: {last_dir}")
        
        folder = QFileDialog.getExistingDirectory(
            self, "选择文件夹", last_dir
        )
        if folder:
            self.write_to_log_display_and_file(f"[INFO] GUI.browse_folder: 用户选择了文件夹: {folder}")
            self.write_to_log_display_and_file(f"[INFO] GUI.browse_folder: 正在扫描文件夹: {folder} ...")
            QApplication.processEvents()
            video_files_in_folder = self._scan_folder_for_videos(folder)
            if video_files_in_folder:
                self._add_paths_to_list(video_files_in_folder)
            else:
                self.write_to_log_display_and_file(f"[INFO] GUI.browse_folder: 在 '{folder}' 中未找到视频文件。")
            
            self.settings.setValue("last_browse_dir", folder)
            self.write_to_log_display_and_file(f"[DEBUG] GUI.browse_folder: 更新上次浏览目录为: {folder}")
        else:
            self.write_to_log_display_and_file("[DEBUG] GUI.browse_folder: 用户未选择文件夹。")


    def clear_file_list(self):
        self.write_to_log_display_and_file("[INFO] GUI.clear_file_list: 清空文件列表和预览。")
        self.file_list.clear()
        self.progress_bar.setValue(0)
        self.preview_table.setRowCount(0)
        self.tmdb_title_label.setText("TMDB标题: N/A")
        self.tmdb_year_label.setText("TMDB年份: N/A")


    def parse_regex_rules(self):
        self.write_to_log_display_and_file("[DEBUG] GUI.parse_regex_rules: 开始解析正则规则。")
        rules = []
        raw_text = self.regex_rules_edit.toPlainText()
        self.write_to_log_display_and_file(f"[DEBUG] GUI.parse_regex_rules: 原始正则文本:\n{raw_text}")
        for line_num, line in enumerate(raw_text.splitlines()):
            line = line.strip()
            if not line or line.startswith("#"):
                self.write_to_log_display_and_file(f"[DEBUG] GUI.parse_regex_rules: 第 {line_num+1} 行被忽略 (空或注释)。")
                continue
            if '=>' in line:
                parts = line.split('=>', 1)
                if len(parts) == 2 and parts[0].strip():
                    pattern = parts[0].strip()
                    replacement = parts[1].strip()
                    rules.append((pattern, replacement))
                    self.write_to_log_display_and_file(f"[DEBUG] GUI.parse_regex_rules: 解析到规则: '{pattern}' => '{replacement}'")
                else:
                    self.write_to_log_display_and_file(f"[WARNING] GUI.parse_regex_rules: 第 {line_num+1} 行格式无效: '{line}'")
            else:
                self.write_to_log_display_and_file(f"[WARNING] GUI.parse_regex_rules: 第 {line_num+1} 行缺少 '=>' 分隔符: '{line}'")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.parse_regex_rules: 解析完成，共 {len(rules)} 条规则。")
        return rules


    def start_processing(self, preview_only=False):
        self.write_to_log_display_and_file(f"[INFO] GUI.start_processing: 用户请求开始处理。预览模式: {preview_only}")
        file_text = self.file_list.toPlainText()
        if not file_text.strip():
            QMessageBox.warning(self, "警告", "请先添加文件或文件夹！")
            self.write_to_log_display_and_file("[WARNING] GUI.start_processing: 文件列表为空，操作中止。")
            return
        
        file_paths_from_gui = [p.strip() for p in file_text.splitlines() if p.strip()]
        if not file_paths_from_gui:
            QMessageBox.warning(self, "警告", "文件列表在去除空行后为空！")
            self.write_to_log_display_and_file("[WARNING] GUI.start_processing: 文件列表在去除空行后为空，操作中止。")
            return

        self.progress_bar.setValue(0)
        self.preview_table.setRowCount(0)
        self.write_to_log_display_and_file(f"--- 开始{'预览' if preview_only else '执行'}重命名 ({datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ---")
        self.start_time = time.time()

        self.browse_files_btn.setEnabled(False); self.browse_folder_btn.setEnabled(False)
        self.clear_btn.setEnabled(False); self.preview_btn.setEnabled(False)
        self.execute_btn.setEnabled(False); self.cancel_btn.setEnabled(True)
        self.custom_season_checkbox.setEnabled(False)
        self.custom_season_input.setEnabled(False)
        self.custom_tmdbid_checkbox.setEnabled(False)
        self.custom_tmdbid_input.setEnabled(False)
        self.custom_tmdb_media_type_combo.setEnabled(False)
        self.fetch_tmdb_info_button.setEnabled(False)
        self.custom_episode_offset_checkbox.setEnabled(False)
        self.custom_episode_offset_input.setEnabled(False)
        self.write_to_log_display_and_file("[DEBUG] GUI.start_processing: UI控件已禁用。")

        moviepilot_api_token = self.moviepilot_api_token_input.text().strip()
        moviepilot_server_url = self.moviepilot_api_server_input.text().strip()
        tmdb_api_key = self.tmdb_api_key_input.text().strip()
        rename_format = self.rename_format_combo.currentText().strip()
        folder_format_template = self.folder_format_input.text().strip()
        season_format_template = self.season_format_input.text().strip()
        regex_rules = self.parse_regex_rules()

        custom_season_enabled = self.custom_season_checkbox.isChecked()
        custom_season_value = self.custom_season_input.text().strip() if custom_season_enabled else ""
        custom_tmdbid_enabled = self.custom_tmdbid_checkbox.isChecked()
        custom_tmdbid_value = self.custom_tmdbid_input.text().strip() if custom_tmdbid_enabled else ""
        custom_tmdb_media_type = self.custom_tmdb_media_type_combo.currentData() if custom_tmdbid_enabled else "tv"
        custom_episode_offset_enabled = self.custom_episode_offset_checkbox.isChecked()
        custom_episode_offset_value_str = self.custom_episode_offset_input.text().strip() if custom_episode_offset_enabled else ""

        folder_rename_only_enabled = self.folder_rename_only_checkbox.isChecked()
        folder_rename_format = self.folder_rename_format_input.text().strip()
        dest_mode = "custom" if self.dest_radio_custom.isChecked() else "current"
        custom_dest_path = self.custom_dest_path_edit.text().strip()
        
        # 获取文件夹结构选项
        use_category_folders = self.use_category_folders_checkbox.isChecked()
        use_year_folders = self.use_year_folders_checkbox.isChecked()

        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: MoviePilot API服务器: {moviepilot_server_url}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: MoviePilot API Token: {'***' if moviepilot_api_token else '未设置'}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: TMDB API Key: {'***' if tmdb_api_key else '未设置'}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 文件名格式: {rename_format}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 主文件夹格式: {folder_format_template}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 季文件夹格式: {season_format_template}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 正则规则数量: {len(regex_rules)}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 自定义季度: 启用={custom_season_enabled}, 值='{custom_season_value}'")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 自定义TMDBID: 启用={custom_tmdbid_enabled}, 值='{custom_tmdbid_value}', 类型='{custom_tmdb_media_type}'")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 自定义集数偏移: 启用={custom_episode_offset_enabled}, 值='{custom_episode_offset_value_str}'")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: --- 文件夹整理模式 ---")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 启用: {folder_rename_only_enabled}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 格式: {folder_rename_format}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 目标模式: {dest_mode}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 自定义目标路径: {custom_dest_path}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 使用二级分类文件夹: {use_category_folders}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 使用年份文件夹: {use_year_folders}")
        self.write_to_log_display_and_file(f"[DEBUG] GUI.start_processing: 待处理文件数量: {len(file_paths_from_gui)}")

        if custom_tmdbid_enabled and not custom_tmdbid_value:
            QMessageBox.warning(self, "自定义TMDBID错误", "已勾选自定义TMDBID，但未填写ID。")
            self.write_to_log_display_and_file("[ERROR] GUI.start_processing: 自定义TMDBID已勾选但ID为空。")
            self.reset_ui_states(); return
        if custom_tmdbid_enabled and not tmdb_api_key:
            QMessageBox.warning(self, "自定义TMDBID错误", "已勾选自定义TMDBID，但未在设置中填写TMDB API Key。")
            self.write_to_log_display_and_file("[ERROR] GUI.start_processing: 自定义TMDBID已勾选但TMDB API Key为空。")
            self.reset_ui_states(); self.tab_widget.setCurrentIndex(1); return
        if custom_season_enabled and not custom_season_value:
            QMessageBox.warning(self, "自定义季度错误", "已勾选自定义季度，但未填写季号。")
            self.write_to_log_display_and_file("[ERROR] GUI.start_processing: 自定义季度已勾选但季号为空。")
            self.reset_ui_states(); return
        if custom_season_enabled and custom_season_value:
            try: int(custom_season_value)
            except ValueError:
                QMessageBox.warning(self, "自定义季度错误", "自定义季号必须为数字。")
                self.write_to_log_display_and_file(f"[ERROR] GUI.start_processing: 自定义季号 '{custom_season_value}' 不是有效数字。")
                self.reset_ui_states(); return
        
        if custom_episode_offset_enabled:
            if not custom_episode_offset_value_str:
                QMessageBox.warning(self, "自定义集数偏移错误", "已勾选自定义集数偏移，但未填写偏移值。")
                self.write_to_log_display_and_file("[ERROR] GUI.start_processing: 自定义集数偏移已勾选但偏移值为空。")
                self.reset_ui_states(); return
            try: 
                int(custom_episode_offset_value_str)
            except ValueError:
                QMessageBox.warning(self, "自定义集数偏移错误", "自定义集数偏移值必须为整数。")
                self.write_to_log_display_and_file(f"[ERROR] GUI.start_processing: 自定义集数偏移值 '{custom_episode_offset_value_str}' 不是有效整数。")
                self.reset_ui_states(); return

        if folder_rename_only_enabled:
            if dest_mode == "custom" and not custom_dest_path:
                QMessageBox.warning(self, "目标目录错误", "已选择自定义目标目录，但路径为空。")
                self.write_to_log_display_and_file("[ERROR] GUI.start_processing: 文件夹整理模式下，自定义目标目录为空。")
                self.reset_ui_states(); return
            if dest_mode == "custom" and not os.path.isdir(custom_dest_path):
                QMessageBox.warning(self, "目标目录错误", "指定的自定义目标目录不存在或不是一个文件夹。")
                self.write_to_log_display_and_file(f"[ERROR] GUI.start_processing: 自定义目标目录 '{custom_dest_path}' 无效。")
                self.reset_ui_states(); return
            if not folder_rename_format:
                QMessageBox.warning(self, "格式错误", "文件夹整理模式的名称格式不能为空。")
                self.write_to_log_display_and_file("[ERROR] GUI.start_processing: 文件夹名称格式为空。")
                self.reset_ui_states(); return

        if not folder_rename_only_enabled and not all([moviepilot_api_token, moviepilot_server_url, rename_format, folder_format_template]):
            QMessageBox.warning(self, "配置错误", "请在'设置'中完整配置MoviePilot API和所有格式！\n（季文件夹格式可以为空）")
            self.write_to_log_display_and_file("[ERROR] GUI.start_processing: MoviePilot API或格式配置不完整。")
            self.reset_ui_states(); self.tab_widget.setCurrentIndex(1); return

        self.worker = RenameWorker(
            file_paths_from_gui, moviepilot_api_token, moviepilot_server_url,
            rename_format, folder_format_template, season_format_template,
            regex_rules, preview_only,
            custom_season_enabled, custom_season_value,
            custom_tmdbid_enabled, custom_tmdbid_value,
            custom_tmdb_media_type,
            custom_episode_offset_enabled, custom_episode_offset_value_str,
            tmdb_api_key,
            folder_rename_only_enabled, folder_rename_format,
            dest_mode, custom_dest_path,
            use_category_folders, use_year_folders
        )
        self.worker.log_signal.connect(self.write_to_log_display_and_file)
        self.worker.progress_signal.connect(self.update_progress)
        self.worker.preview_signal.connect(self.update_preview_table)
        self.worker.finished_signal.connect(lambda results: self.processing_finished(results, preview_only))
        self.worker.start()
        self.write_to_log_display_and_file(f"[INFO] GUI.start_processing: RenameWorker线程已启动。")


    def write_to_log_display_and_file(self, message: str):
        self.log_output.append(message)
        self.log_output.ensureCursorVisible()
        if self.enable_file_log_checkbox.isChecked() and self.log_file_handle and not self.log_file_handle.closed:
            try:
                self.log_file_handle.write(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}] {message}\n")
                self.log_file_handle.flush()
            except Exception as e:
                print(f"错误: 无法写入日志文件: {e}") 


    def cancel_processing(self):
        self.write_to_log_display_and_file("[INFO] GUI.cancel_processing: 用户请求取消操作。")
        if self.worker and self.worker.isRunning():
            self.write_to_log_display_and_file("[DEBUG] GUI.cancel_processing: RenameWorker正在运行，发送中断请求。")
            self.worker.requestInterruption()
        else:
            self.write_to_log_display_and_file("[DEBUG] GUI.cancel_processing: RenameWorker未运行或已完成。")
            self.reset_ui_states()


    def update_progress(self, value):
        self.progress_bar.setValue(value)
        if value % 10 == 0 or value == 100 or value == 0:
             self.write_to_log_display_and_file(f"[DEBUG] GUI.update_progress: 进度更新为 {value}%")

    def update_preview_table(self, orig_fn, new_fn, f_name, s_f_name):
        if self.folder_rename_only_checkbox.isChecked():
            orig_path, new_path = orig_fn, new_fn
            self.preview_table.setHorizontalHeaderLabels(["原文件夹路径", "目标路径 (含季文件夹)", "包含文件数", "模式"])
            self.preview_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
            self.preview_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
            
            row = self.preview_table.rowCount()
            self.preview_table.insertRow(row)
            self.preview_table.setItem(row, 0, QTableWidgetItem(orig_path))
            self.preview_table.setItem(row, 1, QTableWidgetItem(new_path))
            self.preview_table.setItem(row, 2, QTableWidgetItem(f_name))
            self.preview_table.setItem(row, 3, QTableWidgetItem(s_f_name))
            self.preview_table.scrollToBottom()
            return

        self.write_to_log_display_and_file(f"[DEBUG] GUI.update_preview_table: 添加预览行: 原='{orig_fn}', 新='{new_fn}', 主文件夹='{f_name}', 季文件夹='{s_f_name}'")
        row = self.preview_table.rowCount()
        self.preview_table.insertRow(row)
        self.preview_table.setItem(row, 0, QTableWidgetItem(orig_fn))
        self.preview_table.setItem(row, 1, QTableWidgetItem(new_fn))
        self.preview_table.setItem(row, 2, QTableWidgetItem(f_name))
        self.preview_table.setItem(row, 3, QTableWidgetItem(s_f_name))
        self.preview_table.scrollToBottom()


    def processing_finished(self, results_summary, preview_only):
        end_time = time.time()
        duration = end_time - self.start_time
        msg = ""

        # Case 1: Preview mode
        if preview_only:
            action = "预览"
            # In preview, we count items from the table.
            processed_count = self.preview_table.rowCount()
            if self.folder_rename_only_checkbox.isChecked():
                 msg = f"{action}完成！计划分析 {processed_count} 个文件夹。"
            else:
                 msg = f"{action}完成！显示 {processed_count} 个文件的潜在更改。"
            if processed_count == 0:
                 msg = f"{action}完成，但没有文件或文件夹符合条件。请检查日志。"
        
        # Case 2: Execution mode
        else:
            action = "整理" if self.folder_rename_only_checkbox.isChecked() else "重命名"
            files_moved = results_summary.get('moved_files', 0)
            folders_moved = results_summary.get('moved_folders', 0)
            
            if files_moved == 0 and folders_moved == 0:
                 total_processed = results_summary.get('total_processed', 0)
                 msg = f"{action}完成！共检查 {total_processed} 个项目，无需移动。"
            else:
                msg_parts = []
                if folders_moved > 0:
                    msg_parts.append(f"{folders_moved} 个文件夹")
                if files_moved > 0:
                    msg_parts.append(f"{files_moved} 个文件")
                
                if msg_parts:
                    msg = f"{action}完成！成功移动了 " + " 和 ".join(msg_parts) + "。"
                else: # Should not be reached due to the check above, but as a fallback
                    msg = f"{action}完成！"

        final_msg = f"{msg}\n总耗时: {duration:.2f} 秒。"
        QMessageBox.information(self, "完成", final_msg)
        self.write_to_log_display_and_file(f"\n--- {msg} ({datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ---")
        self.write_to_log_display_and_file(f"--- 总耗时: {duration:.2f} 秒 ---")
        self.reset_ui_states()


    def reset_ui_states(self):
        self.write_to_log_display_and_file("[DEBUG] GUI.reset_ui_states: 重置UI控件状态。")
        self.browse_files_btn.setEnabled(True); self.browse_folder_btn.setEnabled(True)
        self.clear_btn.setEnabled(True); self.preview_btn.setEnabled(True)
        self.execute_btn.setEnabled(True); self.cancel_btn.setEnabled(False)
        
        self.custom_season_checkbox.setEnabled(True)
        self.custom_tmdbid_checkbox.setEnabled(True)
        self.custom_episode_offset_checkbox.setEnabled(True)

        self.custom_season_input.setEnabled(self.custom_season_checkbox.isChecked())
        self.custom_tmdbid_input.setEnabled(self.custom_tmdbid_checkbox.isChecked())
        self.custom_tmdb_media_type_combo.setEnabled(self.custom_tmdbid_checkbox.isChecked())
        self.fetch_tmdb_info_button.setEnabled(self.custom_tmdbid_checkbox.isChecked())
        self.custom_episode_offset_input.setEnabled(self.custom_episode_offset_checkbox.isChecked())

        if self.worker and (self.worker.isFinished() or not self.worker.isRunning()):
            self.write_to_log_display_and_file("[DEBUG] GUI.reset_ui_states: RenameWorker已完成或不存在，置为None。")
            self.worker = None
        self.progress_bar.setValue(0)


    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            self.write_to_log_display_and_file("[DEBUG] GUI.dragEnterEvent: 接受拖放操作。")
            event.acceptProposedAction()
        else:
            self.write_to_log_display_and_file("[DEBUG] GUI.dragEnterEvent: 忽略拖放操作 (无URL)。")
            event.ignore()


    def _clean_dropped_uri_to_os_path(self, uri_string: str) -> str | None:
        self.write_to_log_display_and_file(f"[DEBUG] GUI._clean_dropped_uri_to_os_path: 清理URI '{uri_string}'")
        path = uri_string.strip()
        if not path: return None
        
        qurl_temp = QUrl(uri_string)
        if qurl_temp.isLocalFile():
            local_path = qurl_temp.toLocalFile()
            if local_path:
                self.write_to_log_display_and_file(f"[DEBUG] GUI._clean_dropped_uri_to_os_path: QUrl解析为 '{local_path}'")
                return os.path.normpath(local_path)

        if path.startswith('file:////'):
            path = path[len('file:'):]
        elif path.startswith('file:///'):
            path = path[len('file:///'):]
            if os.name != 'nt' and not path.startswith('/'):
                path = '/' + path
        elif path.startswith('file://'):
             path = path[len('file://'):]
        elif path.startswith('file:/'):
             path = path[len('file:/'):]

        if os.name == 'nt' and path.startswith('/') and not path.startswith('//') and len(path) > 2 and path[2] == ':':
            path = path[1:]
        
        path = path.replace('%20', ' ')
        
        try:
            norm_path = os.path.normpath(path)
            self.write_to_log_display_and_file(f"[DEBUG] GUI._clean_dropped_uri_to_os_path: 字符串操作后规范化为 '{norm_path}'")
            return norm_path
        except Exception as e:
            self.write_to_log_display_and_file(f"[ERROR] GUI._clean_dropped_uri_to_os_path: 规范化路径 '{path}' 时出错: {e}. 返回原始清理路径。")
            return path


    def dropEvent(self, event):
        self.write_to_log_display_and_file(f"[INFO] GUI.dropEvent: 检测到拖放操作，将清空当前列表。")
        self.file_list.clear()
        self.preview_table.setRowCount(0)

        self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 接收到拖放事件。MIME数据URL数量: {len(event.mimeData().urls()) if event.mimeData().hasUrls() else 0}")
        paths_to_add_to_gui_list = []
        if event.mimeData().hasUrls():
            for url_obj in event.mimeData().urls():
                local_file_path = None
                raw_uri_string = url_obj.toString()

                if url_obj.isLocalFile():
                    local_file_path = url_obj.toLocalFile()
                    self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: URL.toLocalFile() 结果: '{local_file_path}' for '{raw_uri_string}'")
                
                path_for_os_check = local_file_path

                if not path_for_os_check or not path_for_os_check.strip():
                    self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: toLocalFile() 失败/为空 for '{raw_uri_string}', 尝试手动清理URI。")
                    path_for_os_check = self._clean_dropped_uri_to_os_path(raw_uri_string)

                if not path_for_os_check or not path_for_os_check.strip():
                    self.write_to_log_display_and_file(f"[WARNING] GUI.dropEvent: 无法从URL '{raw_uri_string}' 清理出有效OS路径。")
                    if any(raw_uri_string.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                        self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 尝试直接添加原始URI '{raw_uri_string}' (基于扩展名)。")
                        paths_to_add_to_gui_list.append(raw_uri_string)
                    continue
                
                self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 用于OS检查的路径: '{path_for_os_check}' (来自: '{raw_uri_string}')")
                try:
                    path_for_os_check_norm = os.path.normpath(path_for_os_check)
                    
                    if os.path.isdir(path_for_os_check_norm):
                        self.write_to_log_display_and_file(f"[INFO] GUI.dropEvent: 扫描拖入的文件夹: {path_for_os_check_norm} ...")
                        QApplication.processEvents()
                        video_files = self._scan_folder_for_videos(path_for_os_check_norm)
                        if video_files:
                            self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 文件夹扫描到 {len(video_files)} 个视频文件。")
                            paths_to_add_to_gui_list.extend(video_files)
                        else:
                            self.write_to_log_display_and_file(f"[INFO] GUI.dropEvent: 在文件夹 '{path_for_os_check_norm}' 中未找到视频文件。")
                    elif os.path.isfile(path_for_os_check_norm):
                        self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 拖入路径为文件: '{path_for_os_check_norm}'")
                        if os.path.splitext(path_for_os_check_norm)[1].lower() in VIDEO_EXTENSIONS:
                            self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 添加拖入的视频文件: '{path_for_os_check_norm}'")
                            paths_to_add_to_gui_list.append(path_for_os_check_norm)
                        else:
                            self.write_to_log_display_and_file(f"[INFO] GUI.dropEvent: 忽略拖入的非视频文件: {os.path.basename(path_for_os_check_norm)}")
                    else:
                        self.write_to_log_display_and_file(f"[WARNING] GUI.dropEvent: 路径 '{path_for_os_check_norm}' 在文件系统不存在或不是文件/目录。")
                        if url_obj.scheme() != 'file' or any(raw_uri_string.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                             self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 添加疑似远程或特殊URI的视频文件: {raw_uri_string}")
                             paths_to_add_to_gui_list.append(raw_uri_string)
                        else:
                             self.write_to_log_display_and_file(f"[INFO] GUI.dropEvent: 忽略拖入的未知路径或非视频文件: {raw_uri_string}")

                except Exception as e:
                    self.write_to_log_display_and_file(f"[ERROR] GUI.dropEvent: 检查拖拽路径 '{path_for_os_check}' (来自 '{raw_uri_string}') 时出错: {e}\n{traceback.format_exc()}。")
                    if any(raw_uri_string.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                         self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 出错后，尝试按原样添加原始URI: {raw_uri_string}")
                         paths_to_add_to_gui_list.append(raw_uri_string)
            
            if paths_to_add_to_gui_list:
                self._add_paths_to_list(paths_to_add_to_gui_list)
            event.acceptProposedAction()
        else:
            self.write_to_log_display_and_file(f"[DEBUG] GUI.dropEvent: 拖放事件不包含URL。")
            event.ignore()


    def closeEvent(self, event):
        self.write_to_log_display_and_file("[INFO] GUI.closeEvent: 程序正在关闭。")
        self._save_settings_to_ini()

        if self.log_file_handle and not self.log_file_handle.closed:
            ts = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.log_file_handle.write(f"--- Log ended at {ts} ---\n")
            try:
                self.log_file_handle.flush()
                self.log_file_handle.close()
                self.write_to_log_display_and_file("[DEBUG] GUI.closeEvent: 日志文件已关闭。")
            except Exception as e:
                print(f"关闭日志文件句柄时出错: {e}")
            self.log_file_handle = None

        if self.worker and self.worker.isRunning():
            self.write_to_log_display_and_file("[WARNING] GUI.closeEvent: 检测到RenameWorker仍在运行。")
            reply = QMessageBox.question(self, '退出确认', "任务仍在运行，确定退出吗？\n未完成的重命名将不会被处理。",
                                           QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
                                           QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                self.write_to_log_display_and_file("[INFO] GUI.closeEvent: 用户选择退出，尝试取消运行中的任务。")
                self.cancel_processing()
                if self.worker:
                    self.write_to_log_display_and_file("[DEBUG] GUI.closeEvent: 等待RenameWorker线程结束 (最多1.5秒)...")
                    if not self.worker.wait(1500):
                        self.write_to_log_display_and_file("[WARNING] GUI.closeEvent: RenameWorker在等待后仍在运行。可能需要强制终止或用户需手动结束进程。")
                    else:
                        self.write_to_log_display_and_file("[DEBUG] GUI.closeEvent: RenameWorker线程已结束。")
                event.accept()
            else:
                self.write_to_log_display_and_file("[INFO] GUI.closeEvent: 用户选择不退出。")
                event.ignore()
        else:
            self.write_to_log_display_and_file("[DEBUG] GUI.closeEvent: RenameWorker未运行，正常退出。")
            event.accept()

    def browse_dest_folder(self):
        self.write_to_log_display_and_file("[DEBUG] GUI.browse_dest_folder: 打开目标文件夹选择对话框。")
        last_dir = self.settings.value("custom_dest_path", os.path.expanduser("~"))
        folder = QFileDialog.getExistingDirectory(
            self, "选择统一的目标根目录", last_dir
        )
        if folder:
            self.write_to_log_display_and_file(f"[INFO] GUI.browse_dest_folder: 用户选择了目标目录: {folder}")
            self.custom_dest_path_edit.setText(folder)
        else:
            self.write_to_log_display_and_file("[DEBUG] GUI.browse_dest_folder: 用户未选择目标目录。")

    def update_folder_mode_ui(self, is_folder_mode):
        self.write_to_log_display_and_file(f"[DEBUG] GUI.update_folder_mode_ui: 文件夹整理模式: {is_folder_mode}")
        
        self.folder_format_widget.setEnabled(is_folder_mode)
        self.dest_group.setEnabled(is_folder_mode)
        
        # 启用/禁用文件夹结构选项
        folder_structure_group = None
        for child in self.folder_rename_only_checkbox.parent().findChildren(QGroupBox):
            if child.title() == "文件夹结构选项":
                folder_structure_group = child
                break
        
        if folder_structure_group:
            folder_structure_group.setEnabled(is_folder_mode)
        
        format_group = None
        for child in self.settings_tab.findChildren(QGroupBox):
            if child.title() == "重命名格式":
                format_group = child
                break
        
        if not format_group:
            self.write_to_log_display_and_file("[ERROR] GUI.update_folder_mode_ui: 找不到'重命名格式'组。")
            return
        
        format_group_widgets = [
            self.rename_format_combo,
            self.folder_format_input,
            self.season_format_input,
            self.show_placeholders_btn
        ]
        
        format_labels = format_group.findChildren(QLabel)
        for label in format_labels:
            if is_folder_mode:
                label.setStyleSheet("color: gray;")
            else:
                label.setStyleSheet("")
        
        if is_folder_mode:
            for widget in format_group_widgets:
                widget.setEnabled(False)
            
            if not hasattr(self, 'folder_mode_hint_label'):
                self.folder_mode_hint_label = QLabel("<font color='red'>⚠️ 文件夹整理模式已启用，单文件重命名设置暂不可用</font>")
                format_group.layout().addWidget(self.folder_mode_hint_label)
            else:
                self.folder_mode_hint_label.setVisible(True)
        else:
            for widget in format_group_widgets:
                widget.setEnabled(True)
            
            if hasattr(self, 'folder_mode_hint_label'):
                self.folder_mode_hint_label.setVisible(False)
        
        is_custom_dest = self.dest_radio_custom.isChecked()
        self.custom_dest_path_edit.setEnabled(is_folder_mode and is_custom_dest)
        self.browse_dest_path_btn.setEnabled(is_folder_mode and is_custom_dest)

    def toggle_dark_mode(self, enabled):
        if enabled:
            self.setStyleSheet(self.dark_style_sheet)
            self.write_to_log_display_and_file("[INFO] GUI.toggle_dark_mode: 夜间模式已启用。")
        else:
            self.setStyleSheet("") # Revert to default
            self.write_to_log_display_and_file("[INFO] GUI.toggle_dark_mode: 夜间模式已禁用。")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    try:
        window = VideoRenamerGUI()
        window.show()
        sys.exit(app.exec())
    except Exception as e:
        print(f"程序启动时发生严重错误: {e}")
        detailed_error = traceback.format_exc()
        print(detailed_error)
        
        try:
            err_dlg = QMessageBox()
            err_dlg.setIcon(QMessageBox.Icon.Critical)
            err_dlg.setWindowTitle("程序启动严重错误")
            err_dlg.setText(f"程序启动时发生无法恢复的错误: {str(e)}")
            info_text = detailed_error
            if len(info_text) > 1000:
                info_text = info_text[:1000] + "\n... (更多信息请查看控制台输出)"
            err_dlg.setInformativeText(info_text)
            err_dlg.exec()
        except Exception as E_dlg:
            print(f"创建错误对话框失败: {E_dlg}")
        sys.exit(1)